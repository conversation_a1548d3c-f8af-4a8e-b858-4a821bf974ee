# app/routes/auth.py
from flask import Blueprint, request, jsonify, current_app
from app import db, jwt
from app.models.user import User
from app.models.shift import Shift
from app.schemas.user_schema import user_schema # Para la respuesta
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from datetime import timedelta, datetime
import logging

# Logger para este módulo
logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    if not data or not data.get('folio') or not data.get('password'):
        return jsonify({"msg": "Falta folio o contraseña"}), 400

    user = User.query.filter_by(folio=data['folio']).first()

    if user and user.check_password(data['password']):
        if not user.is_active:
             return jsonify({"msg": "Usuario inactivo"}), 401

        identity_to_store = user.folio

        # Define los claims adicionales ANTES de llamar a create_access_token
        additional_claims = {"role": user.role, "user_id": user.id}

        # --- UNA SOLA LLAMADA A create_access_token ---
        access_token = create_access_token(
            identity=identity_to_store,                 # Argumento 1
            expires_delta=timedelta(hours=8),           # Argumento 2
            additional_claims=additional_claims         # Argumento 3 (SIN coma si es el último)
        )
        # --- FIN DE LA LLAMADA ---

        user_info = user_schema.dump(user)
        return jsonify(access_token=access_token, user=user_info), 200
    else:
        return jsonify({"msg": "Folio o contraseña incorrectos"}), 401

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_me():
    current_user_folio = get_jwt_identity() # Ahora esto es el FOLIO del usuario
    # Buscar usuario por FOLIO
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
         return jsonify({"msg": "Usuario no encontrado (token inválido?)"}), 404
    return jsonify(logged_in_as=user_schema.dump(user)), 200

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    Endpoint de logout que finaliza automáticamente cualquier turno activo del usuario.
    Esto evita que queden turnos huérfanos cuando el agente cierra sesión.
    """
    current_user_folio = get_jwt_identity()
    logger.info(f"Iniciando logout para usuario: {current_user_folio}")

    # Buscar usuario por folio
    user = User.query.filter_by(folio=current_user_folio).first()
    if not user:
        logger.error(f"Token válido pero usuario con folio '{current_user_folio}' no encontrado en BD.")
        return jsonify({"msg": "Usuario del token no encontrado"}), 404

    # Buscar turno activo (sin end_time)
    active_shift = Shift.query.filter_by(user_id=user.id, end_time=None).first()

    if active_shift:
        # Finalizar el turno automáticamente
        active_shift.end_time = datetime.utcnow()
        shift_id = active_shift.id

        try:
            db.session.add(active_shift)
            db.session.commit()
            logger.info(f"Turno {shift_id} finalizado automáticamente durante logout de {user.folio}")

            return jsonify({
                "msg": "Logout exitoso",
                "shift_ended": True,
                "shift_id": shift_id,
                "details": f"Turno {shift_id} finalizado automáticamente"
            }), 200

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error finalizando turno {shift_id} durante logout de {user.folio}: {e}", exc_info=True)
            return jsonify({
                "msg": "Logout exitoso pero error finalizando turno",
                "shift_ended": False,
                "error": str(e)
            }), 200  # 200 porque el logout en sí fue exitoso
    else:
        logger.info(f"Logout de {user.folio} - No había turno activo para finalizar")
        return jsonify({
            "msg": "Logout exitoso",
            "shift_ended": False,
            "details": "No había turno activo"
        }), 200

@auth_bp.route('/cleanup-orphaned-shifts', methods=['POST'])
@jwt_required()
def cleanup_orphaned_shifts():
    """
    Endpoint para limpiar turnos huérfanos (turnos abiertos por más de X horas).
    Solo disponible para supervisores o para uso administrativo.
    """
    from flask_jwt_extended import get_jwt

    # Verificar que el usuario tenga permisos (opcional)
    claims = get_jwt()
    user_role = claims.get('role', 'agente')

    if user_role not in ['supervisor', 'comando', 'admin']:
        return jsonify({"msg": "Permisos insuficientes"}), 403

    current_user_folio = get_jwt_identity()
    logger.info(f"Iniciando limpieza de turnos huérfanos por usuario: {current_user_folio}")

    # Buscar turnos abiertos por más de 12 horas
    cutoff_time = datetime.utcnow() - timedelta(hours=12)
    orphaned_shifts = Shift.query.filter(
        Shift.end_time.is_(None),
        Shift.start_time < cutoff_time
    ).all()

    if not orphaned_shifts:
        logger.info("No se encontraron turnos huérfanos para limpiar")
        return jsonify({
            "msg": "No hay turnos huérfanos para limpiar",
            "shifts_cleaned": 0
        }), 200

    cleaned_count = 0
    cleaned_shifts = []

    for shift in orphaned_shifts:
        try:
            shift.end_time = datetime.utcnow()
            db.session.add(shift)
            cleaned_count += 1
            cleaned_shifts.append({
                "shift_id": shift.id,
                "user_id": shift.user_id,
                "start_time": shift.start_time.isoformat(),
                "mobility_type": shift.mobility_type
            })
            logger.info(f"Turno huérfano {shift.id} finalizado automáticamente")
        except Exception as e:
            logger.error(f"Error finalizando turno huérfano {shift.id}: {e}")

    try:
        db.session.commit()
        logger.info(f"Limpieza completada: {cleaned_count} turnos huérfanos finalizados por {current_user_folio}")

        return jsonify({
            "msg": f"Limpieza completada exitosamente",
            "shifts_cleaned": cleaned_count,
            "cleaned_shifts": cleaned_shifts
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error en commit de limpieza de turnos huérfanos: {e}", exc_info=True)
        return jsonify({
            "msg": "Error durante la limpieza",
            "shifts_cleaned": 0,
            "error": str(e)
        }), 500