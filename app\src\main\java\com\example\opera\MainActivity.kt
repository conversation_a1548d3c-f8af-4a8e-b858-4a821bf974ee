package com.example.opera

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebChromeClient
import android.webkit.PermissionRequest
import android.webkit.ValueCallback
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    // Permisos básicos que se pueden pedir juntos
    private val basicPermissions = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.CAMERA
    )

    // Permisos adicionales que requieren versiones específicas
    private val additionalPermissions = mutableListOf<String>().apply {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            add(Manifest.permission.POST_NOTIFICATIONS)
        }
    }.toTypedArray()

    // Permiso de ubicación en segundo plano (se pide después)
    private val backgroundLocationPermission = arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION)

    companion object {
        // Instancia estática para recibir ubicaciones del servicio
        private var currentInstance: MainActivity? = null

        fun updateLocationFromService(latitude: Double, longitude: Double, accuracy: Float?) {
            currentInstance?.updateWebViewLocation(latitude, longitude, accuracy)
        }
    }

    // Constantes para SharedPreferences (deben coincidir con LocationTrackingService)
    private val PREFS_NAME = "opera_prefs"
    private val KEY_ACCESS_TOKEN = "access_token"
    private val KEY_USER_FOLIO = "user_folio"
    private val KEY_USER_PASSWORD = "user_password"

    // Launcher para permisos básicos
    private val basicPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            Log.d("MainActivity", "✅ Permisos básicos concedidos")
            requestAdditionalPermissions()
        } else {
            Log.w("MainActivity", "❌ Algunos permisos básicos fueron denegados")
            showPermissionDeniedDialog()
        }
    }

    // Launcher para permisos adicionales
    private val additionalPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        Log.d("MainActivity", "✅ Permisos adicionales procesados")
        requestBackgroundLocationPermission()
    }

    // Launcher para ubicación en segundo plano
    private val backgroundLocationLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        Log.d("MainActivity", "✅ Permiso de ubicación en segundo plano procesado")
        startLocationService()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        Log.d("MainActivity", "MainActivity creada")

        // Configurar WebView
        setupWebView()

        // Verificar y solicitar permisos
        checkAndRequestPermissions()
    }

    /**
     * Configura el WebView con todas las opciones necesarias
     */
    private fun setupWebView() {
        webView = findViewById(R.id.webView)

        // Configurar WebSettings
        webView.settings.apply {
            // JavaScript y DOM
            javaScriptEnabled = true
            javaScriptCanOpenWindowsAutomatically = true
            domStorageEnabled = true
            databaseEnabled = true

            // Archivos y contenido
            allowFileAccess = true
            allowContentAccess = true
            allowUniversalAccessFromFileURLs = true
            allowFileAccessFromFileURLs = true

            // Media y cámara
            mediaPlaybackRequiresUserGesture = false

            // Zoom y vista
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            loadWithOverviewMode = true
            useWideViewPort = true

            // Configuraciones adicionales para mejor compatibilidad
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

            // Cache y almacenamiento
            cacheMode = WebSettings.LOAD_DEFAULT
            // setAppCacheEnabled está deprecado en API 33+, se omite

            // Configurar User Agent para mejor compatibilidad
            userAgentString = userAgentString + " OperaApp/1.0"
        }

        // Configurar WebViewClient
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                Log.d("MainActivity", "Navegando a: $url")
                return false // Permitir navegación normal
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d("MainActivity", "Página cargada: $url")

                // Inyectar JavaScript de debug después de cargar la página
                injectDebugJavaScript()
            }

            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                super.onReceivedError(view, errorCode, description, failingUrl)
                Log.e("MainActivity", "Error cargando página: $description")
            }
        }

        // Configurar WebChromeClient para cámara y permisos
        webView.webChromeClient = object : WebChromeClient() {
            override fun onPermissionRequest(request: PermissionRequest?) {
                Log.d("MainActivity", "🎥 Solicitud de permisos WebView: ${request?.resources?.joinToString()}")

                request?.let { permissionRequest ->
                    // Verificar que tenemos los permisos necesarios en Android antes de concederlos al WebView
                    val requiredPermissions = permissionRequest.resources
                    var allPermissionsGranted = true

                    for (resource in requiredPermissions) {
                        when (resource) {
                            PermissionRequest.RESOURCE_VIDEO_CAPTURE -> {
                                if (ContextCompat.checkSelfPermission(this@MainActivity, Manifest.permission.CAMERA)
                                    != PackageManager.PERMISSION_GRANTED) {
                                    allPermissionsGranted = false
                                    Log.w("MainActivity", "❌ Permiso de cámara no concedido en Android")
                                }
                            }
                            PermissionRequest.RESOURCE_AUDIO_CAPTURE -> {
                                // Si necesitáramos micrófono, verificaríamos aquí
                                Log.d("MainActivity", "🎤 Permiso de audio solicitado (no requerido)")
                            }
                        }
                    }

                    if (allPermissionsGranted) {
                        Log.d("MainActivity", "✅ Concediendo permisos al WebView")
                        permissionRequest.grant(requiredPermissions)
                    } else {
                        Log.w("MainActivity", "❌ Denegando permisos al WebView - permisos Android faltantes")
                        permissionRequest.deny()

                        // Mostrar mensaje al usuario
                        runOnUiThread {
                            Toast.makeText(this@MainActivity,
                                "Permisos de cámara requeridos. Por favor, reinicia la aplicación.",
                                Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }

            override fun onConsoleMessage(message: android.webkit.ConsoleMessage?): Boolean {
                message?.let {
                    Log.d("WebView-Console", "${it.messageLevel()}: ${it.message()} -- From line ${it.lineNumber()} of ${it.sourceId()}")
                }
                return true
            }
        }

        // Agregar interfaz JavaScript
        webView.addJavascriptInterface(WebAppInterface(this), "AndroidInterface")
        Log.d("MainActivity", "JavaScript Interface 'AndroidInterface' registrado correctamente")

        // Cargar la URL del simulador
        val url = "https://patagoniaservers.com.ar:5005/_simulate/"
        Log.d("MainActivity", "Cargando URL: $url")
        webView.loadUrl(url)
    }

    /**
     * Inyecta JavaScript de debug para probar la comunicación
     */
    private fun injectDebugJavaScript() {
        val debugJs = """
            console.log('[ANDROID] 🔧 JavaScript de debug inyectado');

            // Verificar AndroidInterface
            if (typeof AndroidInterface !== 'undefined') {
                console.log('[ANDROID] ✅ AndroidInterface disponible');
                console.log('[ANDROID] Métodos disponibles:', Object.keys(AndroidInterface));

                // Probar comunicación
                if (AndroidInterface.debugTest) {
                    AndroidInterface.debugTest('Comunicación JavaScript-Android OK');
                }
            } else {
                console.log('[ANDROID] ❌ AndroidInterface NO disponible');
            }

            // Verificar si el botón existe
            setTimeout(function() {
                const endBtn = document.getElementById('end-shift-btn');
                if (endBtn) {
                    console.log('[ANDROID] ✅ Botón Finalizar Turno encontrado');
                    console.log('[ANDROID] Botón disabled:', endBtn.disabled);
                    console.log('[ANDROID] Botón visible:', !endBtn.classList.contains('d-none'));
                } else {
                    console.log('[ANDROID] ❌ Botón Finalizar Turno NO encontrado');
                }
            }, 2000);
        """.trimIndent()

        webView.evaluateJavascript(debugJs) { result ->
            Log.d("MainActivity", "JavaScript de debug inyectado: $result")
        }
    }

    /**
     * Verifica y solicita los permisos necesarios en etapas
     */
    private fun checkAndRequestPermissions() {
        Log.d("MainActivity", "🔍 Iniciando solicitud de permisos en etapas...")
        requestBasicPermissions()
    }

    /**
     * Solicita permisos básicos (ubicación y cámara)
     */
    private fun requestBasicPermissions() {
        val missingBasic = basicPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingBasic.isEmpty()) {
            Log.d("MainActivity", "✅ Permisos básicos ya concedidos")
            requestAdditionalPermissions()
        } else {
            Log.d("MainActivity", "📱 Solicitando permisos básicos: ${missingBasic.joinToString()}")
            basicPermissionLauncher.launch(missingBasic.toTypedArray())
        }
    }

    /**
     * Solicita permisos adicionales (notificaciones)
     */
    private fun requestAdditionalPermissions() {
        if (additionalPermissions.isEmpty()) {
            Log.d("MainActivity", "⏭️ No hay permisos adicionales para esta versión de Android")
            requestBackgroundLocationPermission()
            return
        }

        val missingAdditional = additionalPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingAdditional.isEmpty()) {
            Log.d("MainActivity", "✅ Permisos adicionales ya concedidos")
            requestBackgroundLocationPermission()
        } else {
            Log.d("MainActivity", "📱 Solicitando permisos adicionales: ${missingAdditional.joinToString()}")
            additionalPermissionLauncher.launch(missingAdditional.toTypedArray())
        }
    }

    /**
     * Solicita permiso de ubicación en segundo plano
     */
    private fun requestBackgroundLocationPermission() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            Log.d("MainActivity", "⏭️ Permiso de ubicación en segundo plano no necesario en esta versión")
            startLocationService()
            return
        }

        val missingBackground = backgroundLocationPermission.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingBackground.isEmpty()) {
            Log.d("MainActivity", "✅ Permiso de ubicación en segundo plano ya concedido")
            startLocationService()
        } else {
            Log.d("MainActivity", "📱 Mostrando explicación para ubicación en segundo plano")
            showBackgroundLocationExplanation()
        }
    }

    /**
     * Muestra explicación antes de pedir ubicación en segundo plano
     */
    private fun showBackgroundLocationExplanation() {
        AlertDialog.Builder(this)
            .setTitle("Ubicación en Segundo Plano")
            .setMessage("Para que la aplicación pueda registrar tu ubicación mientras trabajas, " +
                    "necesitamos acceso a la ubicación incluso cuando la app esté en segundo plano o " +
                    "el teléfono esté bloqueado.\n\n" +
                    "Esto es necesario para el seguimiento GPS durante tu jornada laboral.")
            .setPositiveButton("Continuar") { _, _ ->
                Log.d("MainActivity", "📱 Usuario aceptó, solicitando permiso de ubicación en segundo plano")
                backgroundLocationLauncher.launch(backgroundLocationPermission)
            }
            .setNegativeButton("Omitir") { _, _ ->
                Log.d("MainActivity", "⏭️ Usuario omitió permiso de segundo plano")
                startLocationService()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * Muestra un diálogo cuando los permisos son denegados
     */
    private fun showPermissionDeniedDialog() {
        // No hacer nada, los permisos se pueden volver a solicitar
        Log.w("MainActivity", "Algunos permisos fueron denegados")
    }



    /**
     * Inicia el servicio de seguimiento de ubicación
     */
    private fun startLocationService() {
        Log.d("MainActivity", "Iniciando servicio de seguimiento de ubicación")

        val serviceIntent = Intent(this, LocationTrackingService::class.java)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }

        Toast.makeText(this, "Servicio de ubicación iniciado", Toast.LENGTH_SHORT).show()
    }

    /**
     * Maneja el botón de retroceso
     */
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }

    /**
     * Maneja el resultado de solicitudes de permisos cuando la actividad se reanuda
     */
    override fun onResume() {
        super.onResume()

        // Registrar esta instancia para recibir ubicaciones
        currentInstance = this
        Log.d("MainActivity", "📡 MainActivity registrada para recibir ubicaciones")

        // Verificar si todos los permisos fueron concedidos mientras la app estaba en segundo plano
        val basicGranted = basicPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
        val additionalGranted = additionalPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
        val backgroundGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED
        } else {
            true // No necesario en versiones anteriores
        }

        val allPermissionsGranted = basicGranted && (additionalPermissions.isEmpty() || additionalGranted) && backgroundGranted

        if (allPermissionsGranted) {
            startLocationService()
        }
    }

    override fun onPause() {
        super.onPause()

        // Desregistrar esta instancia
        if (currentInstance == this) {
            currentInstance = null
            Log.d("MainActivity", "📡 MainActivity desregistrada")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("MainActivity", "MainActivity destruida")
    }

    /**
     * Guarda el token de autenticación en SharedPreferences
     */
    fun saveAuthToken(token: String) {
        try {
            val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putString(KEY_ACCESS_TOKEN, token)
                apply()
            }
            Log.d("MainActivity", "Token guardado exitosamente en SharedPreferences")
            Toast.makeText(this, "Autenticación exitosa", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("MainActivity", "Error guardando token en SharedPreferences", e)
        }
    }

    /**
     * Guarda las credenciales del usuario en SharedPreferences
     */
    fun saveUserCredentials(folio: String, password: String) {
        try {
            val sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putString(KEY_USER_FOLIO, folio)
                putString(KEY_USER_PASSWORD, password)
                apply()
            }
            Log.d("MainActivity", "Credenciales de usuario guardadas exitosamente en SharedPreferences")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error guardando credenciales en SharedPreferences", e)
        }
    }

    /**
     * Envía la ubicación actual al JavaScript del WebView
     */
    fun updateWebViewLocation(latitude: Double, longitude: Double, accuracy: Float?) {
        runOnUiThread {
            try {
                val accuracyStr = accuracy?.toString() ?: "null"
                val jsCode = """
                    if (typeof updateLocationFromAndroid === 'function') {
                        updateLocationFromAndroid($latitude, $longitude, $accuracyStr);
                        console.log('[ANDROID_GPS] Ubicación enviada al WebView: $latitude, $longitude');
                    } else {
                        console.log('[ANDROID_GPS] Función updateLocationFromAndroid no encontrada');
                    }
                """.trimIndent()

                webView.evaluateJavascript(jsCode) { result ->
                    Log.d("MainActivity", "JavaScript ejecutado para actualizar ubicación: $result")
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error enviando ubicación al WebView", e)
            }
        }
    }

    /**
     * Método público para ejecutar JavaScript desde WebAppInterface
     */
    fun executeJavaScript(jsCode: String, callback: (String) -> Unit) {
        runOnUiThread {
            webView.evaluateJavascript(jsCode, callback)
        }
    }
}

/**
 * Interfaz JavaScript para comunicación entre WebView y Android
 */
class WebAppInterface(private val context: MainActivity) {

    /**
     * Método de debug para probar la comunicación JavaScript
     */
    @JavascriptInterface
    fun debugTest(message: String) {
        Log.d("WebAppInterface", "🔧 DEBUG TEST: $message")
        context.runOnUiThread {
            Toast.makeText(context, "Debug: $message", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Método llamado desde JavaScript cuando el login es exitoso
     */
    @JavascriptInterface
    fun onLoginSuccess(token: String) {
        Log.d("WebAppInterface", "Token recibido desde JavaScript: ${token.take(50)}...")

        // Ejecutar en el hilo principal de UI
        context.runOnUiThread {
            context.saveAuthToken(token)
        }
    }

    /**
     * Método llamado desde JavaScript para guardar las credenciales del usuario
     */
    @JavascriptInterface
    fun saveCredentials(folio: String, password: String) {
        Log.d("WebAppInterface", "Credenciales recibidas desde JavaScript: folio=$folio")

        // Ejecutar en el hilo principal de UI
        context.runOnUiThread {
            context.saveUserCredentials(folio, password)
        }
    }

    /**
     * Método para mostrar mensajes desde JavaScript
     */
    @JavascriptInterface
    fun showToast(message: String) {
        context.runOnUiThread {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Método específico para debug del botón Finalizar Turno
     */
    @JavascriptInterface
    fun testEndShiftButton() {
        Log.d("WebAppInterface", "🔴 TEST: Botón Finalizar Turno clickeado desde JavaScript")
        context.runOnUiThread {
            Toast.makeText(context, "¡Botón Finalizar Turno funciona!", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Método para forzar la ejecución del botón Finalizar Turno desde Android
     */
    @JavascriptInterface
    fun forceEndShift() {
        Log.d("WebAppInterface", "🔴 FORCE: Forzando finalización de turno desde Android")
        context.runOnUiThread {
            // Ejecutar JavaScript directamente para finalizar turno
            val jsCode = """
                console.log('[ANDROID] 🔴 Forzando finalización de turno...');

                // Verificar que las funciones existen
                if (typeof handleEndShift === 'function') {
                    console.log('[ANDROID] ✅ handleEndShift encontrado, ejecutando...');
                    try {
                        handleEndShift();
                        console.log('[ANDROID] ✅ handleEndShift() ejecutado exitosamente');
                    } catch (error) {
                        console.error('[ANDROID] ❌ Error ejecutando handleEndShift:', error);
                    }
                } else {
                    console.error('[ANDROID] ❌ handleEndShift() no encontrado');
                    console.log('[ANDROID] Funciones disponibles:', Object.getOwnPropertyNames(window).filter(name => name.includes('End') || name.includes('Shift')));
                }
            """.trimIndent()

            context.executeJavaScript(jsCode) { result ->
                Log.d("WebAppInterface", "Resultado de forzar finalización: $result")
            }
        }
    }

    /**
     * Método para verificar el estado del botón Finalizar Turno
     */
    @JavascriptInterface
    fun checkEndShiftButtonState() {
        Log.d("WebAppInterface", "🔍 Verificando estado del botón Finalizar Turno")
        context.runOnUiThread {
            val jsCode = """
                console.log('[ANDROID] 🔍 Verificando estado del botón...');
                const btn = document.getElementById('end-shift-btn');
                if (btn) {
                    console.log('[ANDROID] ✅ Botón encontrado');
                    console.log('[ANDROID] - Disabled:', btn.disabled);
                    console.log('[ANDROID] - Display:', window.getComputedStyle(btn).display);
                    console.log('[ANDROID] - Visibility:', window.getComputedStyle(btn).visibility);
                    console.log('[ANDROID] - Text:', btn.textContent);
                } else {
                    console.log('[ANDROID] ❌ Botón NO encontrado');
                }
            """.trimIndent()

            context.executeJavaScript(jsCode) { result ->
                Log.d("WebAppInterface", "Estado del botón verificado: $result")
            }
        }
    }
}
