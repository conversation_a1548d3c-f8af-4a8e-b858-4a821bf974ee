<!-- templates/simulation/agent_view.html -->
<!doctype html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Simulador <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"/>
    <style>
        /* Estilos (sin cambios) */
        body { background-color: #e9ecef; font-size: 0.95rem; }
        .container-sim { max-width: 450px; margin: auto; background-color: #fff; padding: 15px; border-radius: 8px; margin-top: 15px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .status-bar { background-color: #6c757d; color: white; padding: 8px 12px; font-size: 1em; font-weight: 500; margin-bottom: 15px; border-radius: 4px; text-align: center; }
        .status-bar.active { background-color: #198754; }
        .status-bar.inactive { background-color: #dc3545; }
        .action-button { margin-top: 10px; padding: 12px; font-size: 1.05em; }
        .gps-display { font-family: monospace; background: #f1f1f1; padding: 5px 8px; border-radius: 3px; font-size: 0.9em; display: inline-block; margin-left: 5px; }
        #sim-map { height: 180px; width: 100%; margin-bottom: 10px; border: 1px solid #ccc; border-radius: 4px;}
        .novelty-item { border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; border-radius: 5px; background-color: #f8f9fa; }
        .form-label { margin-bottom: 0.3rem; font-weight: 500;}
        .form-control, .form-select { font-size: 0.95rem; }
        hr { margin: 1rem 0;}
        #novelties-list { max-height: 300px; overflow-y: auto; margin-bottom: 15px; padding-right: 5px; }
        #mobility-options .form-check { margin-bottom: 0.75rem; }
        #mobility-options label { cursor: pointer; }
    </style>
</head>
<body data-api-base-url="{{ API_BASE_URL_FOR_JS }}">

    <div class="container-sim" id="simulation-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            <div id="flash-messages">
              {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
              {% endfor %}
            </div>
          {% endif %}
        {% endwith %}

        <!-- ====================== LOGIN ====================== -->
        <div id="login-section">
            <h3 class="text-center mb-4">OPERA - Agente (Sim)</h3>
            <div id="login-error-sim" class="alert alert-danger d-none mb-3" role="alert"></div>
            <form id="login-form-sim">
                <div class="mb-3">
                    <label for="agent_folio_sim" class="form-label">Agente</label>
                    <select id="agent_folio_sim" name="agent_folio_sim" class="form-select" required>
                         <option value="" disabled selected>-- Seleccionar --</option>
                         {% for agent in agents %}
                            <option value="{{ agent.folio }}">{{ agent.folio }} - {{ agent.name }}</option>
                         {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label for="agent_password_sim" class="form-label">Contraseña</label>
                    <input type="password" id="agent_password_sim" name="agent_password_sim" class="form-control" required value="password"> {# Asumiendo 'password' para dev #}
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary action-button">Ingresar</button>
                </div>
            </form>
        </div>

        <!-- ====================== NOVEDADES ====================== -->
        <div id="novelties-section" class="d-none">
             <h4 class="mb-3 text-center">Novedades Pendientes</h4>
             <div id="novelties-list"><p class="text-center text-muted">Cargando novedades...</p></div>
             <div class="d-grid">
                 {# ESTE BOTÓN AHORA SOLO CONFIRMA LECTURA Y PASA A SELECCIÓN DE MOVILIDAD #}
                 <button id="confirm-novelties-btn" class="btn btn-info action-button" disabled>Confirmar Lectura y Continuar</button>
             </div>
        </div>

        <!-- ====================== SELECCIÓN DE MOVILIDAD (NUEVO) ====================== -->
        <div id="mobility-selection-section" class="d-none">
            <h4 class="mb-3 text-center">Modo de Trabajo</h4>
            <p class="text-center text-muted small">Selecciona cómo te desplazarás en este turno.</p>
            <div id="mobility-error" class="alert alert-danger d-none mb-3" role="alert"></div>
            <div id="mobility-options" class="mb-4">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="mobilityType" id="mobilityPeatonal" value="Peatonal" checked>
                    <label class="form-check-label" for="mobilityPeatonal">
                        <i class="bi bi-person-walking"></i> Peatonal
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="mobilityType" id="mobilityBicicleta" value="Bicicleta">
                    <label class="form-check-label" for="mobilityBicicleta">
                        <i class="bi bi-bicycle"></i> Bicicleta
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="mobilityType" id="mobilityMoto" value="Moto">
                    <label class="form-check-label" for="mobilityMoto">
                        <i class="bi bi-scooter"></i> Moto / Scooter
                    </label>
                </div>
                 <div class="form-check">
                    <input class="form-check-input" type="radio" name="mobilityType" id="mobilityMovil" value="Movil">
                    <label class="form-check-label" for="mobilityMovil">
                        <i class="bi bi-car-front-fill"></i> Móvil Policial
                    </label>
                </div>
            </div>
            <div class="d-grid">
                {# ESTE ES EL NUEVO BOTÓN PARA INICIAR EL TURNO #}
                <button id="confirm-mobility-btn" class="btn btn-success action-button">Confirmar Modo e Iniciar Turno</button>
            </div>
        </div>

        <!-- ====================== TURNO ACTIVO ====================== -->
        <div id="active-shift-section" class="d-none">
            <div id="status-bar" class="status-bar inactive">Turno Inactivo</div>
            <p class="text-center small mb-2">Agente: <strong id="current-agent-folio">N/A</strong> | Modo: <strong id="current-mobility-display">N/A</strong></p>
            <div id="sim-map"></div>
            <div id="agent-alert-placeholder" class="mb-2"></div>
            <p class="text-center small">Ubicación Actual (Sim): <span id="current-gps-display" class="gps-display">N/A</span></p>

            <div class="d-grid gap-2">
                {# SE ELIMINÓ EL BOTÓN MANUAL DE GPS #}
                <button type="button" class="btn btn-warning action-button" data-bs-toggle="modal" data-bs-target="#identifyPersonModal"><i class="bi bi-person-badge"></i> Identificar Persona</button>
                <button type="button" class="btn btn-warning action-button" data-bs-toggle="modal" data-bs-target="#identifyVehicleModal"><i class="bi bi-car-front"></i> Identificar Vehículo</button>
                <button type="button" class="btn btn-warning action-button" data-bs-toggle="modal" data-bs-target="#identifyLicenseModal"><i class="bi bi-person-vcard me-2"></i>Escanear Licencia</button>
                  </div>                <button id="view-novelties-btn" type="button" class="btn btn-secondary action-button" data-bs-toggle="modal" data-bs-target="#allNoveltiesModal"><i class="bi bi-card-list"></i> Ver Todas las Novedades</button>
                <button id="end-shift-btn" class="btn btn-danger action-button"><i class="bi bi-stop-circle"></i> Finalizar Turno</button>
            </div>
        </div>

        <!-- ====================== MODALES ====================== -->
        {% include 'simulation/_modal_identify_person.html' %}
        {% include 'simulation/_modal_identify_vehicle.html' %}
        {% include 'simulation/_modal_identify_license.html' %}
        {% include 'simulation/_modal_all_novelties.html' %}
    </div>

    <!-- ====================== SCRIPTS ====================== -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/dayjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/locale/es.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.10/plugin/localizedFormat.min.js"></script>
    <script> dayjs.locale('es'); dayjs.extend(window.dayjs_plugin_localizedFormat); </script>

    <!-- Escáner y lógica de identificaciones -->
    <script src="https://unpkg.com/html5-qrcode"></script>
    <script src="https://unpkg.com/@zxing/browser@latest"></script>
    <script src="{{ url_for('static', filename='js/scanner.js') }}"></script>
    {# identifications.js puede que no sea necesario aquí, depende si lo usas en el simulador #}
    {# <script src="{{ url_for('static', filename='js/identifications.js') }}"></script> #}
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>

    <!-- Lógica principal del Simulador -->
    <script src="{{ url_for('static', filename='js/agent_simulator.js') }}"></script>
    <script src="{{ url_for('static', filename='js/alerts.js') }}"></script>
    <script>document.addEventListener('DOMContentLoaded', initAgentSimulator);</script>
</body>
</html>