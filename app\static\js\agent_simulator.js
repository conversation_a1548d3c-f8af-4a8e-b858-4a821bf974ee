// app/static/js/agent_simulator.js
// Versión Completa: Flujo con movilidad + GPS Real (Login-Logout) + ID DNI funcionando + WS Corregido

// --- Estado Global del Simulador ---

let currentAgentToken = null;
let currentAgentFolio = null;
let currentShiftId = null;
let selectedMobilityType = 'Peatonal';

// ✅ MOVER ARRIBA la definición
let apiBaseUrl = '/api'; // URL base API REST

// --- Coordenadas y Estado GPS ---
let currentLatitude = -40.8134; // Posición inicial (Viedma default)
let currentLongitude = -62.9967; // Posición inicial (Viedma default)
let lastKnownAccuracy = null; // Última precisión conocida del GPS real
let isUsingRealGps = false; // Flag: true si se obtuvo GPS real exitosamente
let realGpsWatchId = null; // ID del watcher de geolocalización
// --- Fin Estado GPS ---
let map = null; // Instancia del mapa Leaflet
let agentMarker = null; // Marcador del agente
let agentSocket = null; // <<< AÑADIDO >>> Instancia del WebSocket del agente
const API_BASE_URL_WS = 'wss://patagoniaservers.com.ar:5005'; // <<< AÑADIDO >>> URL Base para WS (ajusta si es necesario)

// --- Opciones para la API de Geolocalización ---
const gpsOptions = {
    enableHighAccuracy: true, // Pedir la máxima precisión posible
    timeout: 15000,          // Tiempo máximo de espera para obtener la ubicación (15s)
    maximumAge: 5000           // Usar una ubicación en caché si no tiene más de 5s
};

// --- Elementos del DOM (se asignan en init) ---
let loginSection, noveltiesSection, mobilitySelectionSection, activeShiftSection;
let loginFormSim, loginErrorSim, agentFolioSimSelect, agentPasswordSimInput;
let noveltiesListDiv, confirmNoveltiesBtn, logoutBtnNovelties;
let mobilityOptionsDiv, mobilityErrorDiv, confirmMobilityBtn, logoutBtnMobility;
let statusBar, currentAgentFolioSpan, currentMobilityDisplaySpan, endShiftBtn, logoutBtnActive;
let currentGpsDisplay;
let identifyPersonModal, identifyVehicleModal, allNoveltiesModal; // Instancias Bootstrap Modal
let identifyPersonForm, identifyVehicleForm;
let identifyPersonError, identifyVehicleError;
let identifyPersonBtn, identifyVehicleBtn, viewNoveltiesBtn;

/** Función principal de inicialización */
function initAgentSimulator() {
    console.log("[INIT] Iniciando Simulador de Agente (GPS + Login + WS)...");

     // --- Restaurar sesión persistente si existe token y folio ---
     currentAgentToken = localStorage.getItem('agentToken');
     currentAgentFolio = localStorage.getItem('agentFolio');

     if (currentAgentToken && currentAgentFolio) {
         console.log(`[AUTOLOGIN] Intentando restaurar sesión para el agente: ${currentAgentFolio}`);
         axios.defaults.headers.common['Authorization'] = `Bearer ${currentAgentToken}`;
         axios.get(`${apiBaseUrl}/shifts/current`)
             .then(response => {
                 const shiftData = response.data;
                 if (shiftData?.id) {
                     currentShiftId = shiftData.id;
                     connectAgentWebSocket();
                     initializeAndStartRealGps();
                     updateStatus(`Turno ${currentShiftId} ACTIVO (${shiftData.mobility_type || 'N/A'}) - Restaurado`, true);
                     if (currentAgentFolioSpan) currentAgentFolioSpan.textContent = currentAgentFolio;
                     if (currentMobilityDisplaySpan) currentMobilityDisplaySpan.textContent = shiftData.mobility_type || 'N/A';
                     showSection('active-shift');
                     updateActionButtonState(true);
                 } else {
                     console.warn("[AUTOLOGIN] El token es válido pero no hay turno activo.");
                     showSection('login');
                 }
             })
             .catch(err => {
                 console.warn("[AUTOLOGIN] Falló la restauración automática:", err.response?.data?.msg || err.message);
                 localStorage.removeItem('agentToken');
                 localStorage.removeItem('agentFolio');
                 currentAgentToken = null;
                 currentAgentFolio = null;
                 delete axios.defaults.headers.common['Authorization'];
                 showSection('login');
             });
     } else {
         showSection('login'); // Mostrar login solo si no hay token
     }

    // Configurar URL Base API REST
    apiBaseUrl = document.body.dataset.apiBaseUrl || '/api';
    if (apiBaseUrl.endsWith('/')) apiBaseUrl = apiBaseUrl.slice(0, -1);
    console.log("[INIT] REST API Base URL:", apiBaseUrl);
    console.log("[INIT] WebSocket Base URL:", API_BASE_URL_WS); // Log WS URL

    // --- Asignación de Elementos DOM ---
    loginSection = document.getElementById('login-section');
    noveltiesSection = document.getElementById('novelties-section');
    mobilitySelectionSection = document.getElementById('mobility-selection-section');
    activeShiftSection = document.getElementById('active-shift-section');
    loginFormSim = document.getElementById('login-form-sim');
    loginErrorSim = document.getElementById('login-error-sim');
    agentFolioSimSelect = document.getElementById('agent_folio_sim');
    agentPasswordSimInput = document.getElementById('agent_password_sim');
    noveltiesListDiv = document.getElementById('novelties-list');
    confirmNoveltiesBtn = document.getElementById('confirm-novelties-btn');
    mobilityOptionsDiv = document.getElementById('mobility-options');
    mobilityErrorDiv = document.getElementById('mobility-error');
    confirmMobilityBtn = document.getElementById('confirm-mobility-btn');
    statusBar = document.getElementById('status-bar');
    currentAgentFolioSpan = document.getElementById('current-agent-folio');
    currentMobilityDisplaySpan = document.getElementById('current-mobility-display');
    endShiftBtn = document.getElementById('end-shift-btn');
    currentGpsDisplay = document.getElementById('current-gps-display');

    // Debug: Verificar si el botón se encuentra correctamente
    if (endShiftBtn) {
        console.log("[INIT] ✅ Botón 'Finalizar Turno' encontrado correctamente");
    } else {
        console.error("[INIT] ❌ Botón 'Finalizar Turno' NO encontrado - ID: 'end-shift-btn'");
    }
    identifyPersonBtn = document.querySelector('button[data-bs-target="#identifyPersonModal"]');
    identifyVehicleBtn = document.querySelector('button[data-bs-target="#identifyVehicleModal"]');
    viewNoveltiesBtn = document.getElementById('view-novelties-btn');
    const personModalEl = document.getElementById('identifyPersonModal');
    const vehicleModalEl = document.getElementById('identifyVehicleModal');
    const allNoveltiesModalEl = document.getElementById('allNoveltiesModal');
    // Inicializar instancias de Modal de Bootstrap
    if(personModalEl) identifyPersonModal = new bootstrap.Modal(personModalEl);
    else console.error("[INIT] Bootstrap Modal element #identifyPersonModal not found!");
    if(vehicleModalEl) identifyVehicleModal = new bootstrap.Modal(vehicleModalEl);
    else console.error("[INIT] Bootstrap Modal element #identifyVehicleModal not found!");
    if(allNoveltiesModalEl) allNoveltiesModal = new bootstrap.Modal(allNoveltiesModalEl);
    identifyPersonForm = document.getElementById('identify-person-form');
    identifyVehicleForm = document.getElementById('identify-vehicle-form');
    identifyPersonError = document.getElementById('identify-person-error');
    identifyVehicleError = document.getElementById('identify-vehicle-error');

    // Inicializar mapa Leaflet
    const mapElement = document.getElementById('sim-map');
    if (mapElement) {
        console.log("[INIT] Inicializando mapa con coords por defecto:", currentLatitude, currentLongitude);
        map = L.map(mapElement).setView([currentLatitude, currentLongitude], 14);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19, attribution: '© OpenStreetMap'
         }).addTo(map);
         agentMarker = L.marker([currentLatitude, currentLongitude]).addTo(map).bindPopup("Ubicación Inicial");
         updateGpsDisplay();
    } else {
        console.error("[INIT] Elemento del mapa 'sim-map' no encontrado.");
    }

    // --- Añadir Listeners Generales ---
    if (loginFormSim) loginFormSim.addEventListener('submit', handleLoginSubmit);
    if (confirmNoveltiesBtn) confirmNoveltiesBtn.addEventListener('click', handleConfirmNovelties);
    if (confirmMobilityBtn) confirmMobilityBtn.addEventListener('click', handleConfirmMobilityAndStartShift);
    if (endShiftBtn) {
        console.log("[INIT] ✅ Agregando listener al botón 'Finalizar Turno'");
        endShiftBtn.addEventListener('click', function(event) {
            console.log("[BUTTON] 🔴 Botón 'Finalizar Turno' clickeado!");
            event.preventDefault(); // Prevenir comportamiento por defecto
            event.stopPropagation(); // Evitar propagación del evento

            // Verificar si estamos en Android
            if (typeof AndroidInterface !== 'undefined') {
                console.log("[BUTTON] 🤖 Detectado AndroidInterface - Notificando a Android");
                if (AndroidInterface.testEndShiftButton) {
                    AndroidInterface.testEndShiftButton();
                }
                if (AndroidInterface.debugTest) {
                    AndroidInterface.debugTest("Botón Finalizar Turno clickeado desde WebView");
                }
            }

            // Llamar a la función de finalizar turno
            console.log("[BUTTON] 📞 Llamando a handleEndShift()...");
            handleEndShift();
        });
    } else {
        console.error("[INIT] ❌ No se puede agregar listener - botón 'Finalizar Turno' no encontrado");
    }
    if (viewNoveltiesBtn) viewNoveltiesBtn.addEventListener('click', fetchAndShowAllNovelties);

    // --- Listeners para Modales (con emisión WS corregida) ---
    if (personModalEl) {
        personModalEl.addEventListener('show.bs.modal', event => {
            console.log("[MODAL] Evento 'show.bs.modal' para Persona.");
            try {
                identifyPersonForm?.reset();                             // Limpiar formulario
                const ageInput = document.getElementById('person-age');  // Limpiar campo calculado edad
                if (ageInput) ageInput.value = '';
                showIdentifyPersonError('');                             // Limpiar mensaje de error

                // <<< MODIFICADO >>> Emitir 'start_identification'
                if (agentSocket && agentSocket.connected && currentAgentFolio != null) {
                    console.log(
                      `%c[AGENT WS EMIT] >>> Emitting start_identification (person) for ${currentAgentFolio}`,
                      'color: green; font-weight: bold;'
                    );
                    agentSocket.emit('start_identification', {
                        folio: currentAgentFolio,
                        type: 'person',
                        latitude: currentLatitude,
                        longitude: currentLongitude
                    });
                } else {
                    console.warn(
                      "[AGENT WS EMIT] start_identification(person) no emitido:",
                      "socket.connected=", agentSocket?.connected,
                      "folio=", currentAgentFolio
                    );
                }
            } catch (error) {
                console.error("[MODAL PERSON LISTENER] Error en 'show.bs.modal':", error);
            }
        });
        personModalEl.addEventListener('hidden.bs.modal', event => {
            console.log("[MODAL] Evento 'hidden.bs.modal' para Persona.");
            // stopScannerIfNeededOnModalClose('identifyPersonModal');
        });
    } else {
        console.error("[INIT] No se pudo añadir listener a 'identifyPersonModal'");
    }

    if (vehicleModalEl) {
        vehicleModalEl.addEventListener('show.bs.modal', event => {
            console.log("[MODAL] Evento 'show.bs.modal' para Vehículo.");
            try {
                identifyVehicleForm?.reset();                           // Limpiar formulario
                showIdentifyVehicleError('');                           // Limpiar mensaje de error

                // <<< MODIFICADO >>> Emitir 'start_identification'
                if (agentSocket && agentSocket.connected && currentAgentFolio != null) {
                    console.log(
                      `%c[AGENT WS EMIT] >>> Emitting start_identification (vehicle) for ${currentAgentFolio}`,
                      'color: green; font-weight: bold;'
                    );
                    agentSocket.emit('start_identification', {
                        folio: currentAgentFolio,
                        type: 'vehicle',
                        latitude: currentLatitude,
                        longitude: currentLongitude
                    });
                } else {
                    console.warn(
                      "[AGENT WS EMIT] start_identification(vehicle) no emitido:",
                      "socket.connected=", agentSocket?.connected,
                      "folio=", currentAgentFolio
                    );
                }
            } catch (error) {
                console.error("[MODAL VEHICLE LISTENER] Error en 'show.bs.modal':", error);
            }
        });
        vehicleModalEl.addEventListener('hidden.bs.modal', event => {
            console.log("[MODAL] Evento 'hidden.bs.modal' para Vehículo.");
            // stopScannerIfNeededOnModalClose('identifyVehicleModal');
        });
    } else {
        console.error("[INIT] No se pudo añadir listener a 'identifyVehicleModal'");
    }

    // --- Estado Inicial ---
    console.log("[INIT] Inicialización completada.");
    showSection('login'); // Mostrar login al inicio
    updateActionButtonState(false); // Deshabilitar botones de acción

    // Crear botón de prueba temporal para debug (solo en Android)
    setTimeout(function() {
        if (typeof AndroidInterface !== 'undefined') {
            console.log("[INIT] 🧪 Detectado AndroidInterface - Creando botón de prueba");
            createTestButton();
        }
    }, 3000);
}

/** Muestra una sección y oculta las demás */
function showSection(sectionId) {
    // El código original es correcto.
    [loginSection, noveltiesSection, mobilitySelectionSection, activeShiftSection].forEach(section => {
        if (section) section.classList.add('d-none');
    });
    const sectionToShow = document.getElementById(sectionId + '-section');
    if (sectionToShow) {
        sectionToShow.classList.remove('d-none');
    } else {
        console.error("Sección no encontrada para mostrar:", sectionId);
        showSection('login'); // Volver a login como fallback
    }
}

/** Habilita o deshabilita los botones de acción del turno activo */
function updateActionButtonState(isShiftActive) {
    // El código original es correcto.
    const buttonsToToggle = [endShiftBtn, identifyPersonBtn, identifyVehicleBtn, viewNoveltiesBtn];
    buttonsToToggle.forEach(btn => { if (btn) btn.disabled = !isShiftActive; });
}

// --- Funciones para mostrar errores ---
function showLoginError(message) { if(loginErrorSim){loginErrorSim.textContent = message; loginErrorSim.classList.remove('d-none');}else{alert("Login Error: "+message);} }
function showGeneralError(message) { alert("Error General: " + message); } // Prefijo para diferenciar
function showIdentifyPersonError(message) { if(identifyPersonError){ identifyPersonError.textContent = message; identifyPersonError.classList.remove('d-none'); } else { console.error("ID Person Error (UI Element Missing):", message); } }
function showIdentifyVehicleError(message) { if(identifyVehicleError){ identifyVehicleError.textContent = message; identifyVehicleError.classList.remove('d-none'); } else { console.error("ID Vehicle Error (UI Element Missing):", message); } }
function showMobilityError(message) { if(mobilityErrorDiv){ mobilityErrorDiv.textContent = message; mobilityErrorDiv.classList.remove('d-none'); } else { console.error("Mobility Error (UI Element Missing):", message); } }

/** Maneja el envío del formulario de login */
function handleLoginSubmit(event) {
    event.preventDefault();
    console.log("[LOGIN] Intento de login...");
    showLoginError('');
    const folio = agentFolioSimSelect.value;
    const password = agentPasswordSimInput.value;
    if (!folio || !password) { showLoginError("Selecciona agente e ingresa la contraseña."); return; }
    const submitButton = loginFormSim.querySelector('button[type="submit"]');
    submitButton.disabled = true; submitButton.textContent = 'Ingresando...';

    axios.post(`${apiBaseUrl}/auth/login`, { folio, password })
        .then(response => {
            if (response.data.access_token && response.data.user) {
                currentAgentToken = response.data.access_token;
                currentAgentFolio = response.data.user.folio;
                localStorage.setItem('agentToken', currentAgentToken);
                localStorage.setItem('agentFolio', currentAgentFolio);
                axios.defaults.headers.common['Authorization'] = `Bearer ${currentAgentToken}`;
                console.log("[LOGIN] Login exitoso para:", currentAgentFolio);

                // <<< NUEVO >>> Enviar credenciales a Android para el endpoint anonymous
                console.log("[LOGIN] Verificando AndroidInterface...", typeof AndroidInterface);
                if (typeof AndroidInterface !== 'undefined') {
                    console.log("[LOGIN] AndroidInterface disponible, métodos:", Object.keys(AndroidInterface));
                    if (AndroidInterface.saveCredentials) {
                        console.log("[LOGIN] Enviando credenciales a Android...");
                        AndroidInterface.saveCredentials(folio, password);
                    } else {
                        console.log("[LOGIN] AndroidInterface.saveCredentials NO disponible");
                    }
                } else {
                    console.log("[LOGIN] AndroidInterface NO disponible");
                }

                // <<< MODIFICADO >>> Conectar WebSocket DESPUÉS de obtener token
                connectAgentWebSocket();

                console.log("[LOGIN] Llamando a initializeAndStartRealGps...");
                initializeAndStartRealGps(); // Iniciar intento de GPS REAL

                console.log("[LOGIN] Llamando a fetchUnreadNovelties...");
                fetchUnreadNovelties(); // Esto mostrará la sección de novedades
            } else {
                // Error si la respuesta no tiene token o user
                throw new Error(response.data.msg || "Respuesta de login inválida (faltan datos).");
            }
        })
        .catch(error => {
             console.error('[LOGIN] Login API error:', error.response || error);
             let msg = "Error en la conexión o respuesta del servidor.";
             if (error.response?.data?.msg) msg = error.response.data.msg;
             else if (error.response?.status === 401) msg = "Folio o contraseña incorrectos.";
             else if (error.message) msg = error.message;
             showLoginError(msg);
        })
        .finally(() => {
            submitButton.disabled = false; submitButton.textContent = 'Ingresar';
        });
}

// <<< AÑADIDO >>> Función para conectar WebSocket del Agente (Corregida)
function connectAgentWebSocket() {
    if (!currentAgentToken) {
        console.error("[WS_AGENT] No hay token para conectar WebSocket.");
        return;
    }
    if (agentSocket && agentSocket.connected) {
        console.log("[WS_AGENT] WebSocket ya conectado.");
        return;
    }

    console.log("[WS_AGENT] Intentando conectar WebSocket a:", API_BASE_URL_WS + '/live');
    // Conectar al NAMESPACE /live
    agentSocket = io(API_BASE_URL_WS + '/live', { // Asegúrate que la URL y namespace son correctos
        secure: true,
        transports: ['websocket'],
        auth: { token: currentAgentToken } // Pasar token en la opción 'auth'
    });

    // --- Listeners básicos del Socket ---
    agentSocket.on('connect', () => {
        console.log('[WS_AGENT] Conectado exitosamente al WebSocket /live!');
    });

    agentSocket.on('connect_error', (error) => {
        console.error('[WS_AGENT] Error de conexión WebSocket:', error);
        agentSocket = null; // Limpiar en caso de error de conexión persistente
        // Podrías añadir lógica de reintento aquí o notificar al usuario
    });

    agentSocket.on('auth_error', (error) => { // Espera 'auth_error' del servidor
        console.error('[WS_AGENT] Error de autenticación WebSocket:', error.msg || error);
        alert(`Error de autenticación en tiempo real: ${error.msg || 'Desconocido'}. La sesión se cerrará.`);
        handleLogout(); // Forzar logout si la autenticación WS falla
    });

    agentSocket.on('disconnect', (reason) => {
        console.warn(`[WS_AGENT] Desconectado del WebSocket: ${reason}`);
        // No necesariamente limpiar 'agentSocket = null' aquí si Socket.IO
        // intenta reconectar automáticamente (comportamiento por defecto).
        // Si la desconexión es 'io server disconnect', sí deberías limpiar o forzar logout.
        if (reason === 'io server disconnect') {
            console.warn('[WS_AGENT] Desconexión iniciada por el servidor.');
            // Podrías forzar logout si es por error de auth persistente
        }
    });

    // Escucha para desconexión forzada (ej: sesión duplicada)
     agentSocket.on('force_disconnect', (data) => {
         console.warn(`[WS_AGENT] Desconexión forzada recibida: ${data.reason || 'Razón desconocida'}`);
         alert(`Se ha detectado otra sesión activa (${data.reason || ''}). Esta sesión se cerrará.`);
         handleLogout();
     });

      // ─── Nuevo listener para mensajes del operador ─────────────────────────
  agentSocket.on('operator_message', ({ from, message }) => {
    // Si ya existe, lo borramos (para no duplicar)
    const existing = document.getElementById('operatorIncomingModal');
    if (existing) existing.remove();

    // Construimos el modal al vuelo
    const html = `
      <div class="modal fade" id="operatorIncomingModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header bg-primary text-white">
              <h5 class="modal-title">Mensaje de ${from}</h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <p>${message}</p>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
          </div>
        </div>
      </div>`;
    document.body.insertAdjacentHTML('beforeend', html);
    new bootstrap.Modal(document.getElementById('operatorIncomingModal')).show();
  });

}


/** Busca novedades no leídas */
function fetchUnreadNovelties() {
        console.log("[NOVELTIES] Iniciando fetchUnreadNovelties...");
        if (!currentAgentToken) {
            console.error("[NOVELTIES] Token no presente. Redirigiendo a login.");
            showSection('login');
            return;
        }
        console.log("[NOVELTIES] Token presente:", currentAgentToken);
        if (!noveltiesListDiv || !confirmNoveltiesBtn) {
            console.error("[NOVELTIES] Elementos del DOM para novedades no encontrados.");
            showSection('login');
            return;
        }

         noveltiesListDiv.innerHTML = '<p class="text-center text-muted">Cargando novedades...</p>';
         confirmNoveltiesBtn.disabled = true;
         showSection('novelties'); // Mostrar sección mientras carga
         axios.get(`${apiBaseUrl}/novelties/unread`)
            .then(response => {
                console.log("[NOVELTIES] Respuesta recibida:", response.data);
                renderNovelties(response.data || []);
            })
             .catch(error => {
                 console.error("Error fetching unread novelties:", error.response || error);
                 noveltiesListDiv.innerHTML = '<p class="text-center text-danger">Error al cargar novedades.</p>';
                 // Permitir continuar aunque fallen las novedades
                 confirmNoveltiesBtn.disabled = false;
                 confirmNoveltiesBtn.textContent = 'Continuar (Error Novedades)';
                 confirmNoveltiesBtn.classList.replace('btn-info', 'btn-warning');
             });
     }

/** Renderiza la lista de novedades */
function renderNovelties(novelties) {
    // Código original parece correcto.
     noveltiesListDiv.innerHTML = '';
     confirmNoveltiesBtn.textContent = 'Confirmar Lectura y Continuar';
     confirmNoveltiesBtn.classList.replace('btn-warning', 'btn-info');
     if (!novelties || novelties.length === 0) {
         noveltiesListDiv.innerHTML = '<p class="text-center text-muted">No hay novedades pendientes.</p>';
         confirmNoveltiesBtn.disabled = false; // Habilitar botón si no hay nada que marcar
         return;
     }
     // Checkbox "Marcar todas"
     const selectAllDiv = document.createElement('div');
     selectAllDiv.className = 'form-check mb-3 border-bottom pb-2';
     selectAllDiv.innerHTML = `<input class="form-check-input" type="checkbox" value="" id="check-all-novelties"> <label class="form-check-label fw-bold" for="check-all-novelties">Marcar/Desmarcar todas como leídas</label>`;
     noveltiesListDiv.appendChild(selectAllDiv);
     // Renderizar cada novedad
     novelties.forEach(n => {
         const noveltyDiv = document.createElement('div');
         noveltyDiv.className = 'novelty-item bg-light border rounded p-2 mb-2'; // Mejorar estilo
         const authorDisplay = n.author_user ? `${n.author_user.name || 'N/A'} (${n.author_user.folio})` : 'Sistema';
         const dateDisplay = formatDateTime(n.created_at);
         noveltyDiv.innerHTML = `
             <div class="form-check mb-1">
                <input class="form-check-input novelty-checkbox" type="checkbox" value="${n.id}" id="novelty-${n.id}">
                <label class="form-check-label small text-muted" for="novelty-${n.id}">Marcar leído ID #${n.id}</label>
             </div>
             <p class="mt-1 mb-1">${n.content || '<i>Sin contenido</i>'}</p>
             ${n.image_url ? `<a href="${n.image_url}" target="_blank" class="btn btn-sm btn-outline-secondary my-1">Ver Imagen</a><br>` : ''}
             <small class="text-muted d-block">Autor: ${authorDisplay}</small>
             <small class="text-muted d-block">Creado: ${dateDisplay}</small>
         `;
         noveltiesListDiv.appendChild(noveltyDiv);
     });
     // Lógica para habilitar/deshabilitar botón y marcar/desmarcar todas
     const checkboxes = noveltiesListDiv.querySelectorAll('.novelty-checkbox');
     function checkAllChecked() {
         let allChecked = true;
         if (checkboxes.length === 0) { // Si no hay checkboxes (caso raro)
            allChecked = true; // Permitir continuar
         } else {
             checkboxes.forEach(cb => { if (!cb.checked) allChecked = false; });
         }
         confirmNoveltiesBtn.disabled = !allChecked;
     }
     checkboxes.forEach(cb => cb.addEventListener('change', checkAllChecked));
     const checkAllBox = document.getElementById('check-all-novelties');
     if (checkAllBox) {
        checkAllBox.addEventListener('change', () => {
            checkboxes.forEach(cb => cb.checked = checkAllBox.checked);
            checkAllChecked();
        });
     }
     checkAllChecked(); // Verificar estado inicial
}

/** Confirma lectura de Novedades y pasa a seleccionar movilidad */
function handleConfirmNovelties() {
    // Código original parece correcto.
    if (!currentAgentToken) return;
    confirmNoveltiesBtn.disabled = true;
    confirmNoveltiesBtn.textContent = 'Marcando...'; // Feedback
    const checkedNovelties = noveltiesListDiv.querySelectorAll('.novelty-checkbox:checked');
    const promises = [];
    checkedNovelties.forEach(cb => {
        const noveltyId = cb.value;
        if (noveltyId) { // Asegurarse que tenga valor
             promises.push(
                 axios.post(`${apiBaseUrl}/novelties/${noveltyId}/read`)
                    .then(() => console.log(`[NOVELTY] Novedad ${noveltyId} marcada como leída.`))
                    .catch(err => console.warn(`[NOVELTY] Error marcando ${noveltyId}:`, err.response?.data?.msg || err.message))
            );
        }
    });
    // Esperar que todas las promesas terminen (éxito o fallo)
    Promise.allSettled(promises).then(() => {
        console.log("[NOVELTY] Marcado de novedades completado (o intentos finalizados).");
        showMobilitySelection(); // Pasar a la siguiente sección
    });
}

/** Muestra sección de movilidad */
function showMobilitySelection() {
    // Código original parece correcto.
    console.log("Mostrando selección de movilidad...");
    if (!mobilitySelectionSection) { console.error("Sección movilidad no encontrada."); showGeneralError("Error: No se puede seleccionar movilidad."); return; }
    showMobilityError(''); // Limpiar error previo
    // Asegurar que botón esté habilitado y con texto correcto
    confirmMobilityBtn.disabled = false;
    confirmMobilityBtn.textContent = 'Confirmar Modo e Iniciar Turno';
    showSection('mobility-selection');
}

/** Confirma movilidad e inicia el turno en el backend */
function handleConfirmMobilityAndStartShift() {
    // Código original parece correcto.
    if (!currentAgentToken) return;
    showMobilityError('');
    const selectedRadio = document.querySelector('input[name="mobilityType"]:checked');
    if (!selectedRadio || !selectedRadio.value) { showMobilityError("Debes seleccionar un modo de trabajo."); return; }
    selectedMobilityType = selectedRadio.value; // Guardar selección
    console.log(`Movilidad seleccionada: ${selectedMobilityType}. Iniciando turno...`);
    confirmMobilityBtn.disabled = true; confirmMobilityBtn.textContent = 'Iniciando Turno...';
    startShift(selectedMobilityType); // Llamar a función que interactúa con API
}

/** Registra el inicio del turno en el backend e inicia seguimiento GPS */
function startShift(mobilityType) {
    // Código original parece correcto.
    if (!mobilityType) {
        console.error("startShift llamado sin mobilityType");
        showMobilityError("Error interno: Falta tipo de movilidad.");
        confirmMobilityBtn.disabled = false;
        confirmMobilityBtn.textContent = 'Confirmar Modo e Iniciar Turno';
        return;
    }
    const payload = {
        mobility_type: mobilityType,
        unit_id: 'SIMULATED_UNIT' // Podrías hacerlo más dinámico si es necesario
    };

    axios.post(`${apiBaseUrl}/shifts/start`, payload)
        .then(response => {
            const shiftData = response.data.shift; // Asume que la API devuelve el turno creado
            if (shiftData?.id) {
                currentShiftId = shiftData.id; // GUARDAR ID DEL TURNO ACTIVO
                console.log(`Turno backend iniciado: ${currentShiftId}, Modo: ${mobilityType}`);
                // Actualizar UI con datos del turno activo
                if (currentAgentFolioSpan) currentAgentFolioSpan.textContent = currentAgentFolio || 'N/A';
                if (currentMobilityDisplaySpan) currentMobilityDisplaySpan.textContent = mobilityType;

                // Iniciar seguimiento GPS DESPUÉS de iniciar turno exitosamente
                initializeAndStartRealGps();

                updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - Iniciando GPS...`, true);
                showSection('active-shift'); // Mostrar sección del turno activo
                updateActionButtonState(true); // Habilitar botones de acción
            } else {
                // Error si la respuesta no contiene el ID del turno
                throw new Error(response.data.msg || "Respuesta inválida (ID de turno no recibido).");
            }
        })
        .catch(error => {
            console.error("Error iniciando turno:", error.response || error);
            let msg = "No se pudo iniciar el turno.";
            if (error.response?.status === 409) msg = "Ya tienes un turno activo."; // Conflicto
            else if (error.response?.data?.msg) msg = error.response.data.msg;
            else if (error.message) msg = error.message;
            showMobilityError(msg); // Mostrar error en sección movilidad
            showSection('mobility-selection'); // Mantenerse en selección de movilidad
        })
        .finally(() => {
            // Siempre reactivar el botón, incluso si falló
            confirmMobilityBtn.disabled = false;
            confirmMobilityBtn.textContent = 'Confirmar Modo e Iniciar Turno';
        });
}

// --- FUNCIONES GPS REAL ---

/** Intenta obtener la posición inicial y luego inicia el watch */
function initializeAndStartRealGps() {
    // Código original parece correcto.
    console.log("[GPS_INIT] Intentando inicializar GPS Real...");
    isUsingRealGps = false; // Resetear flag
    updateGpsDisplay(); // Mostrar estado inicial "simulado" o "intentando"

    if (!navigator.geolocation) {
        console.warn("[GPS_INIT] navigator.geolocation NO soportado.");
        alert("Tu navegador no soporta geolocalización. Se usará ubicación simulada por defecto.");
        updateMapLocation(currentLatitude, currentLongitude); // Usar coords por defecto
        updateGpsDisplay();
        return;
    }

    console.log("[GPS_INIT] Llamando a navigator.geolocation.getCurrentPosition...");
    navigator.geolocation.getCurrentPosition(
        (position) => { // Éxito al obtener posición inicial
            console.log("[GPS_INIT] getCurrentPosition ÉXITO:", position.coords);
            isUsingRealGps = true;
            currentLatitude = position.coords.latitude;
            currentLongitude = position.coords.longitude;
            lastKnownAccuracy = position.coords.accuracy;
            updateMapLocation(currentLatitude, currentLongitude);
            updateGpsDisplay(); // Mostrar coords reales
            console.log("[GPS_INIT] Posición inicial obtenida. Llamando a startRealGpsTracking...");
            startRealGpsTracking(); // Iniciar seguimiento continuo
        },
        (error) => { // Error al obtener posición inicial
            console.error("[GPS_INIT] getCurrentPosition ERROR:", error);
            isUsingRealGps = false;
            let errorMsg = "No se pudo obtener GPS inicial.";
            switch(error.code) {
                case error.PERMISSION_DENIED: errorMsg += " Permiso denegado."; stopGpsTracking(); break; // Detener si se deniega permiso
                case error.POSITION_UNAVAILABLE: errorMsg += " Ubicación no disponible."; break;
                case error.TIMEOUT: errorMsg += " Tiempo de espera agotado."; break;
                default: errorMsg += ` Código: ${error.code}.`;
            }
            alert(`Error de GPS: ${errorMsg} Se usará ubicación simulada por defecto.`);
            updateMapLocation(currentLatitude, currentLongitude); // Usar coords por defecto
            updateGpsDisplay();
            // Considerar NO iniciar el watch si getCurrentPosition falla gravemente (ej: permiso denegado)
        },
        gpsOptions // Usar opciones configuradas
    );
    console.log("[GPS_INIT] Llamada a getCurrentPosition realizada (esperando resultado async).");
}

/** Inicia el seguimiento continuo con watchPosition */
function startRealGpsTracking() {
    // Código original parece correcto.
    console.log("[GPS_TRACK] Intentando iniciar watchPosition...");
    stopGpsTracking(); // Detener watcher previo para evitar duplicados

    if (!navigator.geolocation) {
        console.warn("[GPS_TRACK] watchPosition no soportado.");
        return;
    }

    console.log("[GPS_TRACK] Iniciando navigator.geolocation.watchPosition...");
    realGpsWatchId = navigator.geolocation.watchPosition(
        (position) => { // Éxito: Nueva posición recibida
            // console.log("[GPS_TRACK] Nueva Posición Recibida:", position.coords); // Log muy verboso, opcional
            isUsingRealGps = true;
            currentLatitude = position.coords.latitude;
            currentLongitude = position.coords.longitude;
            lastKnownAccuracy = position.coords.accuracy;
            updateMapLocation(currentLatitude, currentLongitude); // Actualizar mapa
            updateGpsDisplay(); // Actualizar texto de coordenadas
            sendRealGpsPoint(currentLatitude, currentLongitude, lastKnownAccuracy); // Enviar punto al backend (si hay turno)
            // Actualizar barra de estado si es necesario (ej: si antes mostraba error GPS)
             if (currentShiftId && statusBar.textContent.includes("Error GPS")) {
                updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - GPS Real OK`, true);
            } else if (currentShiftId && !statusBar.textContent.includes("GPS Real OK")){ // Primera vez OK
                 updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - GPS Real OK`, true);
            }
        },
        (error) => { // Error durante el seguimiento
            console.error("[GPS_TRACK] watchPosition ERROR:", error);
            isUsingRealGps = false; // Marcar que perdimos GPS real
            let errorMsg = "Error de seguimiento GPS.";
            switch(error.code) {
                case error.PERMISSION_DENIED: errorMsg += " Permiso denegado."; stopGpsTracking(); break; // Detener si se revoca permiso
                case error.POSITION_UNAVAILABLE: errorMsg += " Ubicación no disponible."; break;
                case error.TIMEOUT: errorMsg += " Tiempo de espera agotado."; break; // Podría ser temporal
                default: errorMsg += ` Código: ${error.code}.`;
            }
            updateGpsDisplay(); // Mostrar que GPS falló o es simulado
            if (currentShiftId) { // Actualizar status solo si hay turno activo
                 updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - Error GPS (${errorMsg})`, true); // Mantener activo pero indicar error
            } else {
                 console.warn(`[GPS_TRACK] Error GPS (${errorMsg}), pero no hay turno activo.`);
            }
        },
        gpsOptions // Usar opciones configuradas
    );

    // Verificar que watchPosition devolvió un ID
    if (realGpsWatchId === null || realGpsWatchId === undefined) {
         console.error("[GPS_TRACK] watchPosition no devolvió un ID válido. El seguimiento podría no funcionar.");
         if(currentShiftId) updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - Error Fatal GPS Watcher`, true);
    } else {
        console.log(`[GPS_TRACK] watchPosition iniciado con ID: ${realGpsWatchId}`);
    }
}

/** Detiene el seguimiento continuo */
function stopGpsTracking() {
    // Código original parece correcto.
    if (realGpsWatchId !== null) {
        console.log("[GPS_STOP] Deteniendo watchPosition ID:", realGpsWatchId);
        try {
            navigator.geolocation.clearWatch(realGpsWatchId);
        } catch (e) {
            console.error("[GPS_STOP] Error al llamar clearWatch:", e);
        }
        realGpsWatchId = null;
        isUsingRealGps = false; // Marcar que ya no usamos GPS real activamente
        updateGpsDisplay(); // Actualizar display para reflejar que está detenido/simulado
        console.log("[GPS_STOP] Watcher GPS detenido.");
    }
}

/** Envía las coordenadas reales al backend (SOLO SI hay shiftId activo) */
function sendRealGpsPoint(lat, lon, acc) {
    // Código original parece correcto y seguro.
    if (!currentAgentToken) {
        // console.debug("[GPS_SEND] Omitido: No hay token."); // Log opcional muy verboso
        return;
    }
    if (!currentShiftId) {
        // console.debug(`[GPS_SEND] Omitido: Turno no activo (currentShiftId: ${currentShiftId}). GPS sigue funcionando.`); // Log opcional muy verboso
        return; // No enviar si no hay turno activo registrado en el frontend
    }

    const payload = {
        latitude: lat,
        longitude: lon,
        accuracy: acc !== null ? parseFloat(acc.toFixed(1)) : null // Enviar precisión
    };

    // console.debug(`[GPS_SEND] Intentando enviar para turno ${currentShiftId}:`, payload); // Log opcional muy verboso
    axios.post(`${apiBaseUrl}/shifts/track`, payload)
        .then(response => { // Respuesta 201 esperada
            // console.debug(`[GPS_SEND] Punto GPS Real enviado OK para turno ${currentShiftId}.`); // Log opcional muy verboso
             const gpsStatus = isUsingRealGps ? "GPS Real OK" : "GPS Simulado/Error";
             // Solo actualizar barra de estado si previamente indicaba un error de envío
             if (statusBar && statusBar.textContent.includes("Error Envío GPS")) {
                 updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - ${gpsStatus}`, true);
             }
        })
        .catch(error => {
            // Manejo específico si el backend dice que el turno ya no existe (400 o 404)
            if (error.response && (error.response.status === 400 || error.response.status === 404) && error.response.data?.msg?.includes("No hay jornada activa")) {
                 console.warn(`[GPS_SEND] Error ${error.response.status}: El backend indica que el turno ${currentShiftId} ya no está activo.`);
                 currentShiftId = null; // Marcar turno como inactivo en frontend
                 updateStatus(`Turno INACTIVO (Detectado en envío GPS) - GPS Sigue Activo`, false);
                 updateActionButtonState(false); // Deshabilitar botones
                 // Considerar detener el GPS aquí si ya no hay turno? O dejarlo para logout? Depende del flujo deseado.
            } else {
                // Otro tipo de error de red o del servidor
                console.warn("[GPS_SEND] Error enviando punto GPS Real:", error.response?.data?.msg || error.message || error);
                // Indicar error de envío en la barra de estado
                if(currentShiftId) { // Solo si aún creemos tener turno activo
                     updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - Error Envío GPS`, true);
                }
            }
        });
}

// --- FIN FUNCIONES GPS REAL ---


/** Actualizar Barra de Estado */
function updateStatus(message, isActive = false) {
    // Código original parece correcto.
    if(statusBar) {
        statusBar.textContent = message;
        if(isActive) {
            statusBar.classList.remove('inactive', 'bg-danger', 'bg-secondary', 'text-dark');
            statusBar.classList.add('active', 'bg-success', 'text-white'); // Mejor contraste
        } else {
            statusBar.classList.remove('active', 'bg-success', 'text-white');
            statusBar.classList.add('inactive', 'bg-secondary', 'text-dark'); // Estado inactivo/finalizado
        }
    } else {
        console.warn("Elemento statusBar no encontrado para actualizar:", message);
    }
}

/** Actualizar Mapa Leaflet */
function updateMapLocation(lat, lon) {
    // Código original parece correcto.
    if(map && agentMarker) { // Si mapa y marcador existen
        const ll = L.latLng(lat, lon); // Crear objeto LatLng
        agentMarker.setLatLng(ll);
        // Centrar mapa solo si el marcador se sale de la vista actual
        if (!map.getBounds().contains(ll)) {
            map.setView(ll); // Centrar manteniendo zoom
        }
        // Actualizar popup del marcador
        let popupContent = `<b>Ubicación Actual:</b><br>Lat: ${lat.toFixed(5)}<br>Lon: ${lon.toFixed(5)}`;
        if (lastKnownAccuracy !== null) {
             popupContent += `<br>Precisión: ±${lastKnownAccuracy.toFixed(1)}m`;
        }
        agentMarker.setPopupContent(popupContent);
        // Opcional: Abrir popup solo si no está ya abierto?
        // if (!agentMarker.isPopupOpen()) agentMarker.openPopup();

    } else if (map) { // Si mapa existe pero marcador no, crearlo
         console.log("[MAP] Creando marcador inicial en:", lat, lon);
         agentMarker = L.marker([lat, lon]).addTo(map).bindPopup("Ubicación Inicial");
         map.setView([lat,lon], 15); // Zoom razonable al crear marcador
         agentMarker.openPopup(); // Abrir popup al crear
    } else {
        // console.warn("[MAP] Mapa no inicializado, no se puede actualizar ubicación."); // Opcional
    }
}

/** Actualizar Display de Coordenadas GPS */
function updateGpsDisplay() {
    // Código original parece correcto.
    if (currentGpsDisplay) {
        let displayText = `${currentLatitude.toFixed(5)}, ${currentLongitude.toFixed(5)}`;
        if (isUsingRealGps && lastKnownAccuracy !== null) {
            displayText += ` (±${lastKnownAccuracy.toFixed(1)}m)`;
            currentGpsDisplay.classList.remove('text-warning', 'text-danger');
            currentGpsDisplay.classList.add('text-success'); // Indicar GPS OK
        } else if (realGpsWatchId !== null && !isUsingRealGps && navigator.geolocation) { // Watcher activo pero sin posición aún
             displayText = "Obteniendo GPS real...";
             currentGpsDisplay.classList.remove('text-success', 'text-danger');
             currentGpsDisplay.classList.add('text-warning'); // Indicar estado intermedio
        } else { // Sin GPS real (no soportado, error inicial, detenido)
            displayText += ` (Simulado/Default)`;
            currentGpsDisplay.classList.remove('text-success', 'text-warning');
            currentGpsDisplay.classList.add('text-danger'); // Indicar GPS no activo/simulado
        }
        currentGpsDisplay.textContent = displayText;
    }
}

/** Manejar clic en Identificar Persona */
window.handleIdentifyPerson = function() { // Hacerla global si se llama desde HTML
    console.log("[handleIdentifyPerson] Intento de identificación de persona...");
    // 1. Validaciones Previas
    if (!currentAgentToken) { showGeneralError("Error: No estás autenticado."); return; }
    if (!currentShiftId) { showGeneralError("Error: No hay un turno activo para registrar la identificación."); return; }
    if (typeof currentLatitude !== 'number' || typeof currentLongitude !== 'number') {
        console.error("[handleIdentifyPerson] Coordenadas inválidas:", currentLatitude, currentLongitude);
        showIdentifyPersonError("Error: No se pudo obtener la ubicación actual. Intenta de nuevo en unos segundos.");
        return;
    }
    console.log(`[handleIdentifyPerson] Usando coordenadas: Lat=${currentLatitude.toFixed(5)}, Lon=${currentLongitude.toFixed(5)}`);
    showIdentifyPersonError(''); // Limpiar error previo

    // 2. Obtener Datos del Formulario
    const dniRaw = document.getElementById('person-dni-raw')?.value.trim() || null;
    const dniNum = document.getElementById('person-dni')?.value.trim() || null;
    const lastName = document.getElementById('person-lastname')?.value.trim() || null;
    const firstName = document.getElementById('person-firstname')?.value.trim() || null;
    const notes = document.getElementById('person-notes')?.value.trim() || null;
    const gender = document.getElementById('person-gender')?.value || null; // Asume <select>
    const dob = document.getElementById('person-dob')?.value || null; // Asume <input type="date">

    // 3. Validar Datos Mínimos
    // Ajusta esta validación según tus requerimientos exactos
    if (!dniRaw && !dniNum && !(lastName && firstName)) {
        showIdentifyPersonError("Ingresa los datos del DNI escaneado, o el número de DNI, o al menos Apellido y Nombres.");
        return;
    }

    // 4. Preparar Payload para API REST (Incluye todos los campos del form)
    const payloadREST = {
        dni_raw_string: dniRaw, dni_number: dniNum, last_name: lastName,
        first_names: firstName, notes: notes, gender: gender, dob: dob,
        latitude: currentLatitude, longitude: currentLongitude
    };
    console.log("[handleIdentifyPerson] Payload a enviar a API REST:", payloadREST);

    // 5. Deshabilitar Botón Submit del Modal
    const submitButton = document.querySelector('#identifyPersonModal .modal-footer button.btn-primary');
    if (!submitButton) { console.error("Botón de submit del modal persona no encontrado"); return; }
    submitButton.disabled = true; submitButton.textContent = 'Registrando...';

    // 6. Llamada a API REST para Guardar
    axios.post(`${apiBaseUrl}/identifications/person`, payloadREST)
  .then(response => {
    console.log("[handleIdentifyPerson] Respuesta API REST OK:", response.data);
    const savedData = response.data.result || response.data;

    // <<< MODIFICADO >>> EMITIR 'update_identification'
    if (agentSocket && agentSocket.connected && currentAgentFolio != null) {
      console.log(
        `%c[AGENT WS EMIT] >>> Emitting update_identification (person) for ${currentAgentFolio}`,
        'color: green; font-weight: bold;'
      );
      agentSocket.emit('update_identification', {
        folio: currentAgentFolio,
        type: 'person',
        latitude: currentLatitude,
        longitude: currentLongitude,
        identification_data: {
          dni:         savedData.dni_number  || payloadREST.dni_number  || 'S/DNI',
          last_name:   savedData.last_name    || payloadREST.last_name    || '',
          first_names: savedData.first_names  || payloadREST.first_names  || ''
        }
      });
    } else {
      console.warn(
        "[WS_AGENT] update_identification(person) no emitido:",
        "socket.connected=", agentSocket?.connected,
        "folio=", currentAgentFolio
      );
    }
             // <<<--- FIN EMITIR --- >>>

            // 7. Acciones Post-Éxito
            identifyPersonModal.hide(); // Cerrar modal
            alert("Identificación de Persona registrada correctamente."); // Notificar éxito
            identifyPersonForm?.reset(); // Limpiar formulario
             const ageInput = document.getElementById('person-age'); // Limpiar campo edad calculado
             if(ageInput) ageInput.value = '';

        })
        .catch(error => {
            // 8. Manejo de Errores API REST
            console.error("[handleIdentifyPerson] Error en API REST:", error.response || error);
            let errorMsg = "Error desconocido al registrar la identificación.";
             if (error.response?.data?.msg) { // Mensaje específico del backend
                 errorMsg = error.response.data.msg;
             } else if (error.response?.status) { // Código de estado HTTP
                 errorMsg = `Error del servidor (${error.response.status}).`;
             } else if (error.request) { // Error de red
                 errorMsg = "No se pudo conectar con el servidor.";
             } else { // Otro error
                 errorMsg = error.message;
             }
            showIdentifyPersonError(errorMsg); // Mostrar error DENTRO del modal
        })
        .finally(() => {
            // 9. Reactivar Botón Submit (SIEMPRE)
            if(submitButton) {
                 submitButton.disabled = false;
                 submitButton.textContent = 'Registrar Identificación';
            }
        });
}

/** Manejar clic en Identificar Vehículo */
window.handleIdentifyVehicle = function() { // Hacerla global
    console.log("[handleIdentifyVehicle] Intento de identificación de vehículo...");
    // 1. Validaciones Previas (Token, Turno, Coords)
    if (!currentAgentToken || !currentShiftId) { showGeneralError("No hay un turno activo para identificar."); return; }
    if (typeof currentLatitude !== 'number' || typeof currentLongitude !== 'number') {
        console.error("[handleIdentifyVehicle] Coordenadas inválidas:", currentLatitude, currentLongitude);
        showIdentifyVehicleError("Error: No se pudo obtener la ubicación actual. Intenta de nuevo.");
        return;
    }
    console.log(`[handleIdentifyVehicle] Usando coordenadas: Lat=${currentLatitude.toFixed(5)}, Lon=${currentLongitude.toFixed(5)}`);
    showIdentifyVehicleError(''); // Limpiar error previo

    // 2. Obtener Datos del Formulario
    const plate = document.getElementById('vehicle-plate')?.value.trim().toUpperCase() || null; // Patente es clave
    const brand = document.getElementById('vehicle-brand')?.value.trim() || null;
    const model = document.getElementById('vehicle-model')?.value.trim() || null;
    const vehicleType = document.getElementById('vehicle-type')?.value.trim() || null; // Puede ser <select> o <input>
    const color = document.getElementById('vehicle-color')?.value.trim() || null;
    const notes = document.getElementById('vehicle-notes')?.value.trim() || null;
    // Añade aquí otros campos si existen en tu form: credential, chassis, etc.

    // 3. Validar Patente Obligatoria
    if (!plate) {
        showIdentifyVehicleError("El número de Patente/Dominio es obligatorio.");
        return;
    }

    // 4. Preparar Payload para API REST
    const payloadREST = {
       plate: plate, brand: brand, model: model, vehicle_type: vehicleType,
       color: color, notes: notes,
       latitude: currentLatitude, longitude: currentLongitude
       // Añade aquí otros campos si los envías a la API: credential, chassis, etc.
    };
     console.log("[handleIdentifyVehicle] Payload a enviar a API REST:", payloadREST);

    // 5. Deshabilitar Botón Submit
    const submitButton = document.querySelector('#identifyVehicleModal .modal-footer button.btn-primary');
     if (!submitButton) { console.error("Botón de submit del modal vehículo no encontrado"); return; }
    submitButton.disabled = true; submitButton.textContent = 'Registrando...';

    // 6. Llamada a API REST
    axios.post(`${apiBaseUrl}/identifications/vehicle`, payloadREST)
  .then(response => {
    console.log("[handleIdentifyVehicle] Respuesta API REST OK:", response.data);
    const savedData = response.data.result || response.data;

    // <<< MODIFICADO >>> EMITIR 'update_identification'
    if (agentSocket && agentSocket.connected && currentAgentFolio != null) {
      console.log(
        `%c[AGENT WS EMIT] >>> Emitting update_identification (vehicle) for ${currentAgentFolio}`,
        'color: green; font-weight: bold;'
      );
      agentSocket.emit('update_identification', {
        folio: currentAgentFolio,
        type: 'vehicle',
        latitude: currentLatitude,
        longitude: currentLongitude,
        identification_data: {
          plate: savedData.plate  || payloadREST.plate  || 'S/DOM',
          brand: savedData.brand  || payloadREST.brand  || '',
          model: savedData.model  || payloadREST.model  || ''
        }
      });
    } else {
      console.warn(
        "[WS_AGENT] update_identification(vehicle) no emitido:",
        "socket.connected=", agentSocket?.connected,
        "folio=", currentAgentFolio
      );
    }
          // <<<--- FIN EMITIR --- >>>

           // 7. Acciones Post-Éxito
           identifyVehicleModal.hide();
           alert("Identificación de Vehículo registrada correctamente.");
           identifyVehicleForm?.reset();

       })
       .catch(error => {
           // 8. Manejo de Errores API REST
           console.error("[handleIdentifyVehicle] Error en API REST:", error.response || error);
           let errorMsg = "Error desconocido al registrar la identificación.";
            if (error.response?.data?.msg) errorMsg = error.response.data.msg;
            else if (error.response?.status) errorMsg = `Error del servidor (${error.response.status}).`;
            else if (error.request) errorMsg = "No se pudo conectar con el servidor.";
            else errorMsg = error.message;
           showIdentifyVehicleError(errorMsg); // Mostrar error en modal
       })
       .finally(() => {
            // 9. Reactivar Botón Submit
            if(submitButton) {
               submitButton.disabled = false;
               submitButton.textContent = 'Registrar Identificación';
            }
       });
}

/** Obtener y mostrar todas las novedades en Modal */
function fetchAndShowAllNovelties() {
    // Código original parece correcto.
     if (!currentAgentToken) { showGeneralError("No estás autenticado."); return; }
     const modalBody = document.getElementById('all-novelties-modal-body');
     if (!modalBody) { console.error("Elemento 'all-novelties-modal-body' no encontrado."); return; }
     modalBody.innerHTML = '<p class="text-center text-muted">Cargando historial de novedades...</p>';
     allNoveltiesModal.show(); // Mostrar modal mientras carga

     axios.get(`${apiBaseUrl}/novelties/all?per_page=100&order_by=created_at&order_dir=desc`) // Ordenar por más reciente
         .then(response => {
             const novelties = response.data.novelties || response.data || []; // Adaptar a estructura de respuesta
             if (novelties.length === 0) {
                 modalBody.innerHTML = '<p class="text-center text-muted">No hay novedades registradas en el historial.</p>';
                 return;
             }
             modalBody.innerHTML = ''; // Limpiar 'Cargando...'
             novelties.forEach(n => {
                const authorInfo = n.author_user ? `${n.author_user.name || 'N/A'} (${n.author_user.folio})` : 'Sistema';
                const dateInfo = formatDateTime(n.created_at);
                const readStatus = n.read_by_users && n.read_by_users.some(u => u.folio === currentAgentFolio)
                                   ? '<span class="badge bg-success ms-2">Leída</span>'
                                   : '<span class="badge bg-warning text-dark ms-2">No Leída</span>'; // Marcar leídas/no leídas
                const div = document.createElement('div');
                div.className = 'mb-3 border-bottom pb-2';
                div.innerHTML = `
                    <p class="mb-1">${n.content || '<i>Sin contenido</i>'}</p>
                    ${n.image_url ? `<a href="${n.image_url}" target="_blank" class="btn btn-sm btn-outline-info mb-1">Ver Imagen</a><br>` : ''}
                    <small class="text-muted">ID: ${n.id} | Creado: ${dateInfo} por ${authorInfo}</small> ${readStatus}
                `;
                modalBody.appendChild(div);
             });
         })
         .catch(error => {
             console.error("Error fetching all novelties:", error.response || error);
             modalBody.innerHTML = '<p class="text-center text-danger">Error al cargar el historial de novedades.</p>';
         });
}

/** Finalizar Turno y volver al login */
function handleEndShift() {
     console.log("[END_SHIFT] 🔴 FUNCIÓN LLAMADA - Intentando finalizar turno...");
     console.log("[END_SHIFT] Estado actual:");
     console.log("  - currentAgentToken:", currentAgentToken ? "✅ Presente" : "❌ Ausente");
     console.log("  - currentShiftId:", currentShiftId ? `✅ ${currentShiftId}` : "❌ Ausente");
     console.log("  - apiBaseUrl:", apiBaseUrl);

     // Verificar estado antes de proceder
     if (!currentAgentToken) {
         console.error("[END_SHIFT] ❌ No hay token de autenticación");
         showGeneralError("Error: No hay sesión activa. Por favor, inicia sesión nuevamente.");
         showSection('login');
         return;
     }

     if (!currentShiftId) {
         console.error("[END_SHIFT] ❌ No hay turno activo");
         showGeneralError("No hay un turno activo para finalizar.");
         return;
     }

     // Deshabilitar el botón temporalmente para evitar clics múltiples
     if (endShiftBtn) {
         endShiftBtn.disabled = true;
         endShiftBtn.textContent = 'Finalizando...';
     }

     // Confirmación
     console.log("[END_SHIFT] Mostrando confirmación al usuario...");
     if (!confirm("¿Estás seguro de que deseas finalizar el turno actual?\n\nEsto cerrará tu turno y volverás a la pantalla de login.")) {
         console.log("[END_SHIFT] Usuario canceló la confirmación");
         // Restaurar botón
         if (endShiftBtn) {
             endShiftBtn.disabled = false;
             endShiftBtn.textContent = 'Finalizar Turno';
         }
         return;
     }
     console.log("[END_SHIFT] Usuario confirmó - Procediendo con finalización...");

     // Llamada API para finalizar turno
     console.log("[END_SHIFT] 🚀 Enviando request POST a:", `${apiBaseUrl}/shifts/end`);
     console.log("[END_SHIFT] Headers de autorización:", axios.defaults.headers.common['Authorization'] ? "✅ Presente" : "❌ Ausente");

     axios.post(`${apiBaseUrl}/shifts/end`)
        .then(response => {
             const endedShiftId = currentShiftId; // Guardar ID antes de limpiarlo
             console.log(`[END_SHIFT] ✅ Turno ${endedShiftId} finalizado correctamente en backend.`);
             console.log("[END_SHIFT] Respuesta del servidor:", response.data);

             // Notificar a Android si está disponible
             if (typeof AndroidInterface !== 'undefined' && AndroidInterface.debugTest) {
                 AndroidInterface.debugTest(`Turno ${endedShiftId} finalizado exitosamente`);
             }

             // Limpiar estado del turno pero mantener autenticación
             currentShiftId = null;
             selectedMobilityType = 'Peatonal'; // Resetear movilidad
             updateActionButtonState(false);

             // Detener GPS tracking
             stopGpsTracking();

             // Resetear coordenadas a valor por defecto
             currentLatitude = -40.8134;
             currentLongitude = -62.9967;
             isUsingRealGps = false;
             lastKnownAccuracy = null;

             // Actualizar display GPS y mapa
             updateGpsDisplay();
             if(map && agentMarker) {
                 updateMapLocation(currentLatitude, currentLongitude);
             }

             // Volver a la pantalla de login manteniendo la sesión
             showSection('login');
             updateStatus('Turno Finalizado - Selecciona nuevo turno', false);

             // Limpiar formulario de login para permitir nuevo turno
             if (loginFormSim) {
                 loginFormSim.reset();
                 // Restaurar valores por defecto
                 if (agentFolioSimSelect) agentFolioSimSelect.value = '';
                 if (agentPasswordSimInput) agentPasswordSimInput.value = '';
             }
             showLoginError('');
             showMobilityError('');

             // Resetear selección de movilidad en el formulario
             if(mobilityOptionsDiv) {
                 const defaultMobilityRadio = document.getElementById('mobilityPeatonal');
                 if(defaultMobilityRadio) defaultMobilityRadio.checked = true;
                 else {
                      const firstRadio = mobilityOptionsDiv.querySelector('input[type="radio"]');
                      if(firstRadio) firstRadio.checked = true;
                 }
             }

             // Mostrar mensaje de éxito
             alert(`Turno ${endedShiftId} finalizado exitosamente.\n\nPuedes iniciar un nuevo turno cuando lo necesites.`);

             console.log(`[END_SHIFT] ✅ Turno ${endedShiftId} finalizado. Estado reseteado, regresando al login.`);
        })
        .catch(error => {
             console.error("[END_SHIFT] ❌ Error finalizando turno:");
             console.error("  - Error completo:", error);
             console.error("  - Response:", error.response);
             console.error("  - Status:", error.response?.status);
             console.error("  - Data:", error.response?.data);
             console.error("  - Message:", error.message);

             let errorMsg = 'Error desconocido al finalizar turno';
             if (error.response?.data?.msg) {
                 errorMsg = error.response.data.msg;
             } else if (error.response?.status === 404) {
                 errorMsg = 'No se encontró un turno activo para finalizar';
             } else if (error.response?.status === 401) {
                 errorMsg = 'Sesión expirada. Por favor, inicia sesión nuevamente';
             } else if (error.response?.status) {
                 errorMsg = `Error del servidor (${error.response.status})`;
             } else if (error.message) {
                 errorMsg = `Error de conexión: ${error.message}`;
             }

             console.error("[END_SHIFT] Mensaje de error final:", errorMsg);

             // Notificar a Android si está disponible
             if (typeof AndroidInterface !== 'undefined' && AndroidInterface.debugTest) {
                 AndroidInterface.debugTest(`Error finalizando turno: ${errorMsg}`);
             }

             // Mostrar error al usuario
             alert(`Error al finalizar turno:\n\n${errorMsg}\n\nPor favor, intenta nuevamente o contacta al supervisor.`);

             // Si es error 401, forzar logout
             if (error.response?.status === 401) {
                 console.log("[END_SHIFT] Error 401 - Forzando logout");
                 handleLogout();
                 return;
             }

             // No cambiar estado si falló, el turno sigue activo
        })
        .finally(() => {
             // Restaurar botón siempre
             if (endShiftBtn) {
                 endShiftBtn.disabled = false;
                 endShiftBtn.textContent = 'Finalizar Turno';
             }
             console.log("[END_SHIFT] Proceso de finalización completado");
        });
}

/** Manejar Logout (Detiene GPS, desconecta WS y resetea todo) */
function handleLogout() {
    console.log("[LOGOUT] Iniciando cierre de sesión...");

    // 1. Llamar al endpoint de logout del servidor para finalizar turno automáticamente
    if (currentAgentToken) {
        console.log("[LOGOUT] Llamando al endpoint de logout del servidor...");
        axios.post(`${apiBaseUrl}/auth/logout`)
            .then(response => {
                console.log("[LOGOUT] Respuesta del servidor:", response.data);
                if (response.data.shift_ended) {
                    console.log(`[LOGOUT] Turno ${response.data.shift_id} finalizado automáticamente`);
                } else {
                    console.log("[LOGOUT] No había turno activo para finalizar");
                }
            })
            .catch(error => {
                console.warn("[LOGOUT] Error llamando al endpoint de logout:", error.response?.data?.msg || error.message);
                // Continuar con el logout local aunque falle el servidor
            })
            .finally(() => {
                // Ejecutar limpieza local independientemente del resultado del servidor
                performLocalLogout();
            });
    } else {
        console.log("[LOGOUT] No hay token, ejecutando solo limpieza local");
        performLocalLogout();
    }
}

/** Función auxiliar para realizar la limpieza local del logout */
function performLocalLogout() {
    console.log("[LOGOUT] Ejecutando limpieza local...");

    // 1. Detener GPS
    stopGpsTracking();

    // 2. Desconectar WebSocket
    if (agentSocket && agentSocket.connected) {
         console.log("[WS_AGENT] Desconectando WebSocket...");
         agentSocket.disconnect();
         console.log("[WS_AGENT] WebSocket desconectado.");
    } else if (agentSocket) { // Si existe pero no conectado
        console.log("[WS_AGENT] Instancia de WebSocket existía pero no estaba conectada.");
    } else {
        console.log("[WS_AGENT] No había instancia de WebSocket activa.");
    }
    agentSocket = null; // Limpiar la variable SIEMPRE en logout

    // 3. Limpiar Estado Global
    currentAgentToken = null;
    currentAgentFolio = null;
    localStorage.removeItem('agentToken');
    localStorage.removeItem('agentFolio');
    currentShiftId = null;
    selectedMobilityType = 'Peatonal'; // Resetear
    isUsingRealGps = false;
    lastKnownAccuracy = null;
    // Resetear coordenadas a valor por defecto
    currentLatitude = -40.8134;
    currentLongitude = -62.9967;

    // 4. Limpiar Cabecera Axios
    if(axios) axios.defaults.headers.common['Authorization'] = '';
    else console.warn("Axios no disponible para limpiar cabecera.");

    // 5. Resetear UI
    showSection('login'); // Volver a pantalla de login
    updateStatus('Turno Inactivo', false); // Resetear barra de estado
    updateActionButtonState(false); // Deshabilitar botones
    loginFormSim?.reset(); // Limpiar form login
    showLoginError('');
    showMobilityError('');
    // Resetear selección de movilidad en el formulario
    if(mobilityOptionsDiv) {
        const defaultMobilityRadio = document.getElementById('mobilityPeatonal'); // Asume ID del radio Peatonal
        if(defaultMobilityRadio) defaultMobilityRadio.checked = true;
        else { // Fallback si no encuentra el ID específico
             const firstRadio = mobilityOptionsDiv.querySelector('input[type="radio"]');
             if(firstRadio) firstRadio.checked = true;
        }
    }
    updateGpsDisplay(); // Actualizar display GPS a coords por defecto/simulado
    // Resetear mapa
    if(map && agentMarker) {
        updateMapLocation(currentLatitude, currentLongitude); // Mover marcador a default
        // Opcional: Resetear vista del mapa
        // map.setView([currentLatitude, currentLongitude], 14);
    }

    console.log("[LOGOUT] Sesión cerrada completamente. Estado y UI reseteados.");
}

/** Función para recibir ubicación desde Android */
window.updateLocationFromAndroid = function(latitude, longitude, accuracy) {
    console.log(`[ANDROID_GPS] Ubicación recibida de Android: ${latitude}, ${longitude}, precisión: ${accuracy}`);

    // Actualizar variables globales
    currentLatitude = latitude;
    currentLongitude = longitude;
    lastKnownAccuracy = accuracy;
    isUsingRealGps = true;

    // Actualizar display y mapa
    updateGpsDisplay();
    updateMapLocation(latitude, longitude);

    console.log(`[ANDROID_GPS] ✅ Ubicación actualizada en el simulador`);
};

/** Formatear Fecha/Hora usando Day.js si está disponible */
function formatDateTime(isoString) {
    if (!isoString) return "N/A";
    try {
        // Intentar con Day.js si está cargado y configurado
        if(typeof dayjs === 'function' && dayjs?.Ls?.['es']) {
            // Asumiendo que el plugin localizedFormat está extendido globalmente
            // y dayjs.locale('es') fue llamado en algún punto (ej: en el HTML)
            return dayjs(isoString).format('DD/MM/YYYY HH:mm'); // Formato común en Argentina
        } else {
            // Fallback a Date.toLocaleString si Day.js no está listo
            // console.warn("Day.js no disponible/configurado, usando Date.toLocaleString()");
            const date = new Date(isoString);
            if (isNaN(date)) return "Fecha Inválida"; // Chequear si la fecha es válida
            const options = {
                day: '2-digit', month: '2-digit', year: 'numeric',
                hour: '2-digit', minute: '2-digit', hour12: false
            };
            return date.toLocaleString('es-AR', options).replace(',', '');
        }
    } catch (e) {
        console.error("Error formateando fecha:", isoString, e);
        return 'Error Fecha'; // Indicar error en el formato
    }
}

// Listener para asegurar que se llama a init al cargar el DOM
document.addEventListener('DOMContentLoaded', initAgentSimulator);

// FUNCIONES DE DEBUG GLOBALES PARA TESTING
window.debugEndShift = function() {
    console.log("[DEBUG] 🧪 Función de debug manual para Finalizar Turno");

    // Probar comunicación con Android
    if (typeof AndroidInterface !== 'undefined') {
        console.log("[DEBUG] AndroidInterface disponible");
        if (AndroidInterface.testEndShiftButton) {
            console.log("[DEBUG] Llamando testEndShiftButton...");
            AndroidInterface.testEndShiftButton();
        }
        if (AndroidInterface.debugTest) {
            console.log("[DEBUG] Llamando debugTest...");
            AndroidInterface.debugTest("Debug manual desde consola");
        }
    } else {
        console.log("[DEBUG] AndroidInterface NO disponible");
    }

    // Probar función original
    console.log("[DEBUG] Llamando handleEndShift...");
    handleEndShift();
};

window.debugButtonState = function() {
    console.log("[DEBUG] 🔍 Estado del botón Finalizar Turno:");
    const btn = document.getElementById('end-shift-btn');
    if (btn) {
        console.log("  - Botón encontrado: ✅");
        console.log("  - Disabled:", btn.disabled);
        console.log("  - Style display:", btn.style.display);
        console.log("  - Computed style:", window.getComputedStyle(btn).display);
        console.log("  - Parent visible:", btn.parentElement ? !btn.parentElement.classList.contains('d-none') : 'N/A');
    } else {
        console.log("  - Botón encontrado: ❌");
    }
};

// Función para crear botón de prueba temporal
window.createTestButton = function() {
    console.log("[DEBUG] 🧪 Creando botón de prueba temporal...");

    // Eliminar botón anterior si existe
    const existingBtn = document.getElementById('temp-test-btn');
    if (existingBtn) existingBtn.remove();

    // Crear botón de prueba
    const testBtn = document.createElement('button');
    testBtn.id = 'temp-test-btn';
    testBtn.textContent = '🧪 TEST: Finalizar Turno (Android)';
    testBtn.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: #ff4444;
        color: white;
        border: none;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        cursor: pointer;
    `;

    // Agregar listener que llama directamente a Android
    testBtn.addEventListener('click', function() {
        console.log("[TEST] 🔴 Botón de prueba clickeado");

        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.forceEndShift) {
            console.log("[TEST] Llamando AndroidInterface.forceEndShift()");
            AndroidInterface.forceEndShift();
        } else {
            console.log("[TEST] AndroidInterface.forceEndShift no disponible");
            alert('AndroidInterface.forceEndShift no disponible');
        }
    });

    // Agregar al body
    document.body.appendChild(testBtn);
    console.log("[DEBUG] ✅ Botón de prueba creado");
};

// Funcion de volver a su turno activo y no perder tras un refresco de pagina
function restoreActiveShiftIfExists() {
    console.log("[RESTORE] Verificando turno activo del agente...");
    axios.get(`${apiBaseUrl}/shifts/active`)
        .then(response => {
            if (response.data?.shift) {
                const shift = response.data.shift;
                currentShiftId = shift.id;
                selectedMobilityType = shift.mobility_type || 'Peatonal';
                console.log(`[RESTORE] Turno activo restaurado. ID: ${currentShiftId}, Modo: ${selectedMobilityType}`);

                // Actualizar UI
                if (currentAgentFolioSpan) currentAgentFolioSpan.textContent = currentAgentFolio || 'N/A';
                if (currentMobilityDisplaySpan) currentMobilityDisplaySpan.textContent = selectedMobilityType;

                // GPS + botones
                initializeAndStartRealGps();
                updateActionButtonState(true);
                updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - GPS OK`, true);
                showSection('active-shift');
            } else {
                console.log("[RESTORE] No hay turno activo para este agente.");
                showSection('login');
            }
        })
        .catch(err => {
            console.error("[RESTORE] Error verificando turno activo:", err);
            showSection('login'); // fallback
        });
}