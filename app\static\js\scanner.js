// static/js/scanner.js
// Versión completa y corregida

let html5QrCode = null;
let zxingReader = null;
let zxingControls = null; // Variable para guardar controles de ZXing
let currentScannerMode = null;
let activeCameraId = null;
let isScannerBusy = false; // Indica si hay una operación de inicio/escaneo activa
let restartRequested = null;
let stopInProgress = false; // Flag para evitar llamadas concurrentes a stop
let startTimeoutId = null; // Para poder cancelar reinicios agendados
let noCodeFoundErrorCount = 0; // Contador para errores "No code found"
const MAX_NO_CODE_LOGS = 5; // Limitar logs de "No code found"

// Logger
function log(tag, msg, type = "log") {
    const prefix = `[SCANNER][${tag}]`;
    // Filtrar errores repetitivos de "No code found"
    if (tag === 'SCANNING_ERROR' && msg?.includes('No MultiFormat Readers')) {
        noCodeFoundErrorCount++;
        if (noCodeFoundErrorCount > 1 && noCodeFoundErrorCount < MAX_NO_CODE_LOGS) {
             console.log(prefix, '(Advertencia: Código no detectado)'); // Log reducido
        } else if (noCodeFoundErrorCount === MAX_NO_CODE_LOGS) {
             console.warn(prefix, 'Se omitirán más logs de "Código no detectado" para esta sesión.');
        }
        if (noCodeFoundErrorCount >= MAX_NO_CODE_LOGS) return; // Omitir log completo
    } else {
         // Resetear contador si es otro error o un log diferente
         if(tag !== 'SCANNING_ERROR') noCodeFoundErrorCount = 0;
    }

    if (type === "error") console.error(prefix, msg);
    else if (type === "warn") console.warn(prefix, msg);
    else console.log(prefix, msg);
}


async function startScanner(mode = 'dni') {
    console.trace(`[START_SCANNER_TRACE] Llamada a startScanner modo ${mode}`);
    log('START_REQUEST', `Solicitud para iniciar escáner en modo: ${mode}`);

    // Verificar permisos de cámara antes de continuar
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        stream.getTracks().forEach(track => track.stop()); // Detener inmediatamente, solo verificamos permisos
        log('PERMISSIONS', 'Permisos de cámara verificados correctamente');
    } catch (error) {
        log('PERMISSIONS_ERROR', `Error de permisos de cámara: ${error.name} - ${error.message}`, 'error');

        if (error.name === 'NotAllowedError') {
            alert('Permisos de cámara denegados.\n\nPor favor, permite el acceso a la cámara y recarga la página.');
        } else if (error.name === 'NotFoundError') {
            alert('No se encontró ninguna cámara en el dispositivo.');
        } else {
            alert(`Error accediendo a la cámara: ${error.message}`);
        }
        return;
    }

    if (isScannerBusy) {
        log('BUSY', 'Escáner ocupado (inicio o escaneo activo). Se programó reinicio.', 'warn');
        restartRequested = mode;
        if (currentScannerMode && currentScannerMode !== mode && (html5QrCode || zxingReader)) {
             log('MODE_CHANGE', `Se solicitó modo ${mode}, deteniendo modo actual ${currentScannerMode}.`);
             await stopScanner();
        }
        return;
    }

    if (startTimeoutId) {
        clearTimeout(startTimeoutId);
        startTimeoutId = null;
    }
    restartRequested = null;

    isScannerBusy = true;
    currentScannerMode = mode;
    log('INIT', `Iniciando escáner en modo: ${mode}`);

    const containerId =
        mode === 'dni'     ? 'scanner-container-person' :
        mode === 'vehicle' ? 'scanner-container-vehicle' :
                            'scanner-container-license';
    const readerId =
        mode === 'dni'     ? 'reader-person' :
        mode === 'vehicle' ? 'reader-vehicle' :
                            'reader-license';

    const scannerContainer = document.getElementById(containerId);
    if (!scannerContainer) {
        log('ERROR', `Contenedor del escáner con ID '${containerId}' no encontrado`, 'error');
        isScannerBusy = false; currentScannerMode = null;
        return;
    }

    const readerElement = document.getElementById(readerId);
    if (!readerElement) {
        log('ERROR', `Elemento reader con ID '${readerId}' no encontrado`, 'error');
        isScannerBusy = false; currentScannerMode = null;
        return;
    }

    // Verificar tipo de elemento ANTES de iniciar
    const expectedElementType = mode === 'vehicle' ? 'DIV' : 'VIDEO';
    if (readerElement.tagName !== expectedElementType) {
         log('ERROR', `Tipo de elemento reader incorrecto para modo ${mode}. Esperado: ${expectedElementType}, Encontrado: ${readerElement.tagName}`, 'error');
         isScannerBusy = false; currentScannerMode = null;
         const modeName = mode === 'dni' ? 'DNI' :
                 mode === 'vehicle' ? 'Vehículo' :
                 'Licencia';
alert(`Error de configuración: El lector para ${modeName} no es del tipo correcto.`);
         return;
    }

    scannerContainer.style.display = 'block';
    noCodeFoundErrorCount = 0; // Resetear contador de errores al iniciar

    try {
        if (mode === 'vehicle') {
            await startVehicleScanner(readerId);
        } else if (mode === 'dni') {
            await startDniScanner(readerId);
        } else if (mode === 'license') {
                   await startLicenseScanner(readerId);
        }
        log('STARTED', `Escáner modo ${mode} iniciado correctamente.`);
    } catch (err) {
        log('START_FAILED', `Fallo al iniciar escáner modo ${mode}: ${err}`, 'error');
        // stopScanner es llamado dentro de los catch de startVehicle/Dni si fallan
        await stopScanner(true);
    }
}

async function startVehicleScanner(readerId) { // Espera ID de DIV
    // --- Limpieza previa ---
    if (html5QrCode) {
        log('CLEANUP', 'Limpiando instancia html5QrCode previa.');
        await html5QrCode.stop().catch(e => log('CLEANUP_WARN', 'Error menor deteniendo html5QrCode previo: '+e));
        await html5QrCode.clear().catch(e => log('CLEANUP_WARN', 'Error menor limpiando html5QrCode: '+e));
        html5QrCode = null;
    }
    if (zxingReader && typeof zxingReader.reset === 'function') {
        log('CLEANUP', 'Limpiando instancia zxingReader previa (modo vehículo).');
        try { zxingReader.reset(); } catch(e){log('RESET_WARN', `Error menor reseteando zxingReader: ${e}`)}
        zxingReader = null;
        zxingControls = null; // Limpiar controles también
    }
    // --- Fin Limpieza ---

    html5QrCode = new Html5Qrcode(readerId);

    try {
        const devices = await Html5Qrcode.getCameras();
        if (!devices || !devices.length) {
            throw new Error('No se encontraron cámaras disponibles');
        }

        const rearCamera = devices.find(d => d.label.toLowerCase().includes('back') || d.label.toLowerCase().includes('trasera'));
        const preferredCamera = rearCamera || devices[0];
        activeCameraId = preferredCamera.id;
        log('CAMERA_SELECT', `Cámara seleccionada para QR: ${preferredCamera.label} (ID: ${activeCameraId})`);

        await html5QrCode.start(
            { deviceId: { exact: activeCameraId } },
            {
                fps: 10,
                qrbox: (viewfinderWidth, viewfinderHeight) => {
                    let minEdgePercentage = 0.7;
                    let minEdgeSize = Math.min(viewfinderWidth, viewfinderHeight);
                    let qrboxSize = Math.floor(minEdgeSize * minEdgePercentage);
                    return { width: qrboxSize, height: qrboxSize };
                },
                experimentalFeatures: { useBarCodeDetectorIfSupported: false }
            },
            (decodedText, decodeResult) => {
                 log('QR_DECODED', `Texto decodificado: ${decodedText}`);
                 handleDecodedText(decodedText);
            },
            (errorMessage) => {
                if (errorMessage?.includes("NotFoundException") ||errorMessage?.includes("No MultiFormat Readers") || errorMessage?.includes("No barcode")) {
                    if (noCodeFoundErrorCount === 0 || noCodeFoundErrorCount % 50 === 0) {
                         log('SCANNING_INFO', `No se detecta código QR (normal). Count: ${noCodeFoundErrorCount}`);
                    }
                    noCodeFoundErrorCount++;
                    return;
                }
                noCodeFoundErrorCount = 0;
                if (errorMessage?.name === "IndexSizeError") {
                    log('CAMERA_FRAME', "Frame corrupto (ignorado)", 'warn');
                } else if (errorMessage?.name === 'NotAllowedError') {
                    log('CAMERA_PERMISSION', "Permisos de cámara denegados", 'error');
                    alert('Permisos de cámara denegados. Por favor, permite el acceso y recarga la página.');
                    stopScanner(true);
                } else if (errorMessage?.name !== 'OverconstrainedError') {
                    log('SCANNING_WARN', `Error durante escaneo QR: ${errorMessage} (Name: ${errorMessage?.name})`, 'warn');
                }
            }
        ).catch(err => {
            log('HTML5QRCODE_START_ERROR', `Error directo de html5QrCode.start: ${err}`, 'error');

            // Manejar errores específicos
            if (err.name === 'NotAllowedError') {
                alert('Permisos de cámara denegados.\n\nPor favor, permite el acceso a la cámara en la configuración del navegador.');
            } else if (err.name === 'NotFoundError') {
                alert('No se pudo acceder a la cámara.\n\nVerifica que no esté siendo usada por otra aplicación.');
            } else if (err.name === 'OverconstrainedError') {
                alert('La cámara seleccionada no es compatible.\n\nIntentando con otra cámara...');
            }

            throw err; // Re-lanzar para el catch externo
        });
        log('CAMERA_ACTIVE', `Cámara ${activeCameraId} iniciada para QR Vehicular.`);
    } catch (err) {
         log('VEHICLE_SCANNER_INIT_ERROR', `Error general iniciando escáner de vehículo: ${err}`, 'error');

         // Mostrar mensaje específico al usuario
         if (err.message.includes('No se encontraron cámaras')) {
             alert('No se encontraron cámaras disponibles en el dispositivo.');
         } else if (!err.name || err.name === 'Error') {
             alert(`Error iniciando escáner: ${err.message}`);
         }

         await stopScanner(true); // Llama a stopScanner en caso de fallo
         throw err; // Re-lanzar para que startScanner lo vea
    }
}


async function startDniScanner(readerId) { // Espera ID de VIDEO
    // --- Limpieza previa ---
     if (zxingReader || zxingControls) { // Limpiar ZXing si quedó de antes
        log('CLEANUP', '[startDniScanner] Limpiando instancia zxingReader/Controls previa.');
        if (zxingControls && typeof zxingControls.stop === 'function') {
            try { zxingControls.stop(); } catch(e) { log('CLEANUP_WARN', `Error menor deteniendo zxingControls: ${e}`); }
        }
        if (zxingReader && typeof zxingReader.reset === 'function') {
            try { zxingReader.reset(); } catch(e){ log('CLEANUP_WARN', `Error menor reseteando zxingReader: ${e}`); }
        }
        zxingReader = null;
        zxingControls = null;
    }
    if (html5QrCode) { // Limpiar Html5Qrcode si quedó de antes
        log('CLEANUP', '[startDniScanner] Limpiando instancia html5QrCode previa.');
        await html5QrCode.stop().catch(e => log('CLEANUP_WARN', 'Error menor deteniendo html5QrCode previo: '+e));
        await html5QrCode.clear().catch(e => log('CLEANUP_WARN', 'Error menor limpiando html5QrCode: '+e));
        html5QrCode = null;
    }
    // --- Fin Limpieza ---

    zxingReader = new ZXingBrowser.BrowserMultiFormatReader(undefined, 250); // Sin hints
    log('ZXING_INIT', 'ZXing Reader inicializado (sin hints específicos).');

    try {
        const videoInputDevices = await ZXingBrowser.BrowserCodeReader.listVideoInputDevices();
        if (!videoInputDevices || !videoInputDevices.length) { throw new Error('No cameras found'); }
        const rearCamera = videoInputDevices.find(d => d.label.toLowerCase().includes('back') || d.label.toLowerCase().includes('trasera'));
        const preferredCamera = rearCamera || videoInputDevices[0];
        activeCameraId = preferredCamera.deviceId;
        log('CAMERA_SELECT', `Cámara seleccionada para DNI: ${preferredCamera.label} (ID: ${activeCameraId})`);

        // --- Guardar Controles ---
        // Guardamos el objeto devuelto por decodeFromVideoDevice en zxingControls
        zxingControls = await zxingReader.decodeFromVideoDevice(activeCameraId, readerId, (result, err, controls) => {
            // Este es el callback que se ejecuta continuamente
            if (result) {
                log('DNI_DECODED', `Texto detectado: ${result.getText()}`);
                handleDecodedText(result.getText()); // Llama a la función que detiene y procesa
            }
            if (err) {
                // Manejar errores NO fatales (NotFoundException o No MultiFormat)
                if (err.name === 'NotFoundException' || err.message?.includes('No MultiFormat Readers')) {
                     if (noCodeFoundErrorCount === 0 || noCodeFoundErrorCount % 50 === 0) { // Loguear menos frecuentemente
                         log('SCANNING_INFO', `No se detecta código DNI (normal). Count: ${noCodeFoundErrorCount}`);
                     }
                     noCodeFoundErrorCount++;
                    return; // No hacer nada más si es este error común
                 }
                 noCodeFoundErrorCount = 0; // Resetear si hay otro error
                // Loguear otros errores
                if (err.name !== 'NotAllowedError' && err.name !== 'OverconstrainedError') {
                     log('SCANNING_ERROR', `Error durante escaneo DNI: ${err} (Name: ${err.name}, Msg: ${err.message})`);
                }
                // Detener ante errores graves de cámara/permisos
                if (err.name === 'NotReadableError' || err.message?.includes('Could not start video source') || err.name === 'NotAllowedError') {
                    log('CAMERA_ERROR', 'Error grave con la cámara/permisos, deteniendo escáner.', 'error');
                    alert(`Error de cámara: ${err.message}. Verifique los permisos y refresque la página.`);
                    stopScanner(true); // Forzar detención
                }
            } else {
                 noCodeFoundErrorCount = 0; // Resetear contador si no hubo error
            }
        }).catch(err => { // Catch para error AL INICIAR decodeFromVideoDevice
             log('ZXING_DECODE_INIT_ERROR', `Error iniciando decodeFromVideoDevice: ${err}`, 'error');
             zxingControls = null; // Asegurar que controles sea null si falla el inicio
             throw err; // Re-lanzar para el catch externo
        });
        // --- Fin Guardar Controles ---

        log('CAMERA_ACTIVE', `Cámara ${activeCameraId} iniciada para PDF417 (DNI).`);

    } catch (err) { // Catch para errores generales (listar cámaras, etc.)
         log('ZXING_START_ERROR', `Error general iniciando ZXing: ${err}`, 'error');
         await stopScanner(true); // Llama a stopScanner en caso de fallo
         throw err; // Re-lanzar
    }
}

// === Nuevo: arrancar sólo PDF_417 para licencia ===
async function startLicenseScanner(readerId) {
    log('LICENSE_SCANNER_INIT', 'Iniciando startLicenseScanner...');
    // Limpieza previa (sin cambios)
    if (html5QrCode) {
        log('LICENSE_CLEANUP', 'Limpiando html5QrCode previo para licencia.');
        await html5QrCode.stop().catch((e) => log('LICENSE_CLEANUP_WARN', `Error menor deteniendo html5QrCode: ${e}`));
        html5QrCode = null;
    }
    if (zxingControls) {
        log('LICENSE_CLEANUP', 'Limpiando zxingControls previo para licencia.');
        try { zxingControls.stop?.(); } catch(e) { log('LICENSE_CLEANUP_WARN', `Error menor deteniendo zxingControls: ${e}`); }
        zxingControls = null;
    }
    if (zxingReader) {
        log('LICENSE_CLEANUP', 'Limpiando zxingReader previo para licencia.');
        try { zxingReader.reset?.(); } catch(e) { log('LICENSE_CLEANUP_WARN', `Error menor reseteando zxingReader: ${e}`); }
        zxingReader = null;
    }
    log('LICENSE_CLEANUP_DONE', 'Limpieza previa para licencia completada.');

    try {
        // Verificar la disponibilidad de las librerías y sus componentes
        if (typeof ZXingBrowser === 'undefined' || ZXingBrowser === null ||
            typeof ZXingBrowser.BrowserMultiFormatReader === 'undefined' || // Necesario para new ZXingBrowser.BrowserMultiFormatReader
            typeof ZXingBrowser.BrowserCodeReader === 'undefined') {       // Necesario para listVideoInputDevices
            throw new Error('ZXingBrowser o componentes esenciales (BrowserMultiFormatReader, BrowserCodeReader) no están definidos. Verifica la carga del script.');
        }

        // OPCIÓN A: Intentar sin hints explícitos, confiando en la detección de PDF417 por defecto.
        log('LICENSE_HINTS', 'Intentando inicializar lector de licencia sin hints de formato específicos (confiando en detección PDF_417 por defecto).');
        zxingReader = new ZXingBrowser.BrowserMultiFormatReader(undefined, 500); // 'undefined' para hints

        log('LICENSE_READER_CREATED', 'ZXing BrowserMultiFormatReader creado para licencia.');

    } catch (err) {
        log('LICENSE_SETUP_ERROR', `Error creando reader para licencia: ${err}`, 'error');
        isScannerBusy = false;
        currentScannerMode = null;
        alert(`Error al preparar el escáner de licencia: ${err.message}. Por favor, recarga la página.`);
        throw err; // Relanzar para que startScanner lo maneje
    }

    // Elegir cámara (sin cambios respecto a la versión anterior)
    let videoInputDevices;
    try {
        videoInputDevices = await ZXingBrowser.BrowserCodeReader.listVideoInputDevices();
    } catch (err) {
        log('LICENSE_LIST_CAMERAS_ERROR', `Error listando dispositivos de video: ${err}`, 'error');
        throw new Error(`No se pudieron listar las cámaras: ${err.message}`);
    }

    if (!videoInputDevices || videoInputDevices.length === 0) {
        log('LICENSE_CAMERA_ERROR', 'No se encontraron dispositivos de video.', 'error');
        throw new Error('No cameras found for license scanner');
    }
    const preferred = videoInputDevices.find(d => /back|trasera/i.test(d.label)) || videoInputDevices[0];
    activeCameraId = preferred.deviceId;
    log('LICENSE_CAMERA_SELECT', `Cámara seleccionada para licencia: ${preferred.label} (ID: ${activeCameraId})`);

    // Arrancar decodificación (sin cambios respecto a la versión anterior,
    // el manejo de excepciones como NotFoundException, etc., debería seguir funcionando
    // ya que acceden a ZXingBrowser.NotFoundException directamente).
    log('LICENSE_DECODE_START', `Intentando iniciar decodeFromVideoDevice para licencia en reader (elemento video): ${readerId} con cámara ID: ${activeCameraId}`);
    try {
        zxingControls = await zxingReader.decodeFromVideoDevice(
            activeCameraId,
            readerId,
            (result, err, controls) => { // Este es el callback que se ejecuta continuamente
                if (result) {
                    log('LICENSE_DECODED', `Licencia decodificada: ${result.getText()}`);
                    handleDecodedText(result.getText());
                }
                if (err) {
                    // ***** MODIFICACIÓN IMPORTANTE AQUÍ *****
                    // Volver a usar err.name o err.message para identificar errores comunes
                    // ya que las clases de excepción directas (ej. ZXingBrowser.NotFoundException)
                    // parecen no estar disponibles o ser inconsistentes.
                    if (err.name === 'NotFoundException' ||
                        (err.message && (
                            err.message.includes('No MultiFormat Readers') ||
                            err.message.includes('No code found') || // Algunas versiones de ZXing usan este mensaje
                            err.message.includes('碼未找到') // Mensaje en chino que a veces aparece
                        ))) {
                        // Es un error común de "no se encontró código", no es fatal.
                        // Puedes añadir el logueo reducido si quieres, como en DNI:
                        if (noCodeFoundErrorCount === 0 || noCodeFoundErrorCount % 50 === 0) {
                             log('LICENSE_SCANNING_INFO', `No se detecta código de licencia (normal). Count: ${noCodeFoundErrorCount}`);
                        }
                        noCodeFoundErrorCount++;
                        return; // No hacer nada más, seguir escaneando
                    }
                    
                    // Si no es un error de "no encontrado", resetear el contador y loguear el error.
                    noCodeFoundErrorCount = 0;
                    log('LICENSE_SCAN_ERROR', `Error durante escaneo de licencia: ${err.name} - ${err.message}`, 'warn');

                    // Considerar si algún otro error aquí debería detener el escáner,
                    // similar a como lo haces en DNI para NotReadableError, etc.
                    // Por ahora, solo logueamos otros errores como advertencia.
                    // Si ocurren errores graves de cámara (NotAllowedError, NotReadableError),
                    // el .catch() externo de decodeFromVideoDevice debería capturarlos.
                } else {
                    // Si no hay error (result es null pero err también es null), resetear contador.
                    noCodeFoundErrorCount = 0;
                }
            }
        ); // El .catch() para esta promesa se mantiene abajo
        log('CAMERA_ACTIVE', `Licencia: cámara ${activeCameraId} activa (intentando todos los formatos).`);
    } catch (err) { // Este catch es para errores AL INICIAR decodeFromVideoDevice
        log('LICENSE_DECODE_INIT_ERROR', `Error al iniciar decodeFromVideoDevice para licencia: ${err.name} - ${err.message}`, 'error');
        // Si err.name es 'TypeError' y el mensaje es sobre 'instanceof',
        // sabríamos que el problema sigue estando en el manejo de excepciones del callback.
        // Pero con el cambio de arriba, este TypeError específico no debería ocurrir.
        // Otros errores de inicio (ej. cámara no accesible) se capturarían aquí.
        throw err; // Relanzar para que startScanner lo vea y detenga
    }
}

async function stopScanner(startFailed = false) {
    if (stopInProgress) { log('STOP_SKIP', 'Detención ya en progreso.'); return; }
    stopInProgress = true;
    log('STOP', `Intentando detener escáner (Modo activo: ${currentScannerMode}, Fallo inicio?: ${startFailed})...`);

    const personContainer = document.getElementById('scanner-container-person');
    const vehicleContainer = document.getElementById('scanner-container-vehicle');
    const licenseContainer = document.getElementById('scanner-container-license');
    if (personContainer) personContainer.style.display = 'none';
    if (vehicleContainer) vehicleContainer.style.display = 'none';
    if (licenseContainer) licenseContainer.style.display = 'none';

    let stoppedHtml5 = false;
    let stoppedZxing = false;
    let triedZxingStop = false; // Flag para saber si intentamos parar ZXing

    try {
        log('STOP_TRY_BLOCK', `Verificando instancias ANTES de detener. html5QrCode: ${html5QrCode ? 'existe' : 'null'}, zxingReader: ${zxingReader ? 'existe' : 'null'}, zxingControls: ${zxingControls ? 'existe' : 'null'}`);

        // --- Detener Html5Qrcode ---
        if (html5QrCode && typeof html5QrCode.stop === 'function') {
            log('STOPPING_QR', 'Intentando detener Html5Qrcode...');
            try {
                await html5QrCode.stop(); log('STOPPED_QR', 'Html5Qrcode.stop() OK.'); stoppedHtml5 = true;
            } catch (e) { log('STOP_WARN', `Error en html5QrCode.stop(): ${e.message || e}`); }
            // Clear siempre después de intentar stop, incluso si falló
            try {
                await html5QrCode.clear(); log('CLEARED_QR', 'Html5Qrcode.clear() OK.');
            } catch (e) { log('CLEAR_WARN', `Error en html5QrCode.clear(): ${e.message || e}`); }
            html5QrCode = null; // Nullificar después de intentar detener/limpiar
            log('STOP_INFO', '[stopScanner] html5QrCode variable set to null.');
        } else if (html5QrCode) {
             log('STOP_INFO', '[stopScanner] html5QrCode existe pero no tiene método stop válido? Forzando null.');
             html5QrCode = null; // Forzar null si la instancia es inválida
        }

        // --- Detener ZXing ---
        if (zxingReader || zxingControls) { // Intentar detener si existe el reader O los controles
             triedZxingStop = true; // Marcamos que intentamos detener ZXing
             log('STOPPING_DNI', `Intentando detener ZXing. Reader: ${zxingReader ? 'existe' : 'null'}, Controls: ${zxingControls ? 'existe' : 'null'}`);

             // 1. Usar controls.stop() si está disponible
             if (zxingControls && typeof zxingControls.stop === 'function') {
                  log('STOPPING_DNI', 'Llamando a zxingControls.stop()...');
                  try {
                      zxingControls.stop(); // Intenta detener el stream/cámara
                      log('STOPPED_DNI', 'zxingControls.stop() ejecutado.');
                      stoppedZxing = true; // Considerar detenido si controls.stop() no lanzó error grave
                  } catch (e) {
                      // Atrapar específicamente el error 'setPhotoOptions failed' o 'UnknownError'
                      if (e.message?.includes('setPhotoOptions failed') || e.name === 'UnknownError') {
                           log('STOP_WARN', `Error conocido interno de ZXing al detener (setPhotoOptions/Unknown): ${e.message || e.name}`);
                           stoppedZxing = true; // Aún así, consideramos que se intentó detener
                      } else {
                           log('STOP_WARN', `Error inesperado llamando a zxingControls.stop(): ${e}`);
                           // Si hay otro error, podríamos no marcar stoppedZxing, pero igual limpiamos abajo
                      }
                  }
             } else { log('STOPPING_DNI', 'No se encontraron controles válidos (zxingControls).'); }

             // 2. Llamar a zxingReader.reset() SIEMPRE que zxingReader exista, como limpieza adicional/fallback
             if (zxingReader && typeof zxingReader.reset === 'function') {
                 log('STOPPING_DNI', 'Llamando a zxingReader.reset() como limpieza/fallback...');
                 try {
                     zxingReader.reset();
                     log('STOPPED_DNI', 'zxingReader.reset() ejecutado.');
                     stoppedZxing = true; // Si reset funciona, definitivamente está detenido
                 } catch (e) { log('RESET_WARN', `Error durante zxingReader.reset(): ${e}`); }
             } else if (zxingReader) {
                  log('STOPPING_DNI', 'Instancia zxingReader existe pero no tiene método reset.');
             } else {
                  // Esto es normal si zxingControls.stop() ya limpió todo
                  log('STOPPING_DNI', 'Instancia zxingReader no encontrada al intentar reset() (puede ser normal).');
             }
        }
        // 3. Limpiar variables globales DESPUÉS de intentar detener
        zxingReader = null;
        zxingControls = null;
        if(triedZxingStop) log('STOP_INFO', '[stopScanner] Variables zxingReader y zxingControls set to null.');


        // --- Log final ---
        if (stoppedHtml5 || stoppedZxing) {
            log('STOP_SUCCESS', `Intento de detención finalizado. Html5 detenido: ${stoppedHtml5}, Zxing detenido: ${stoppedZxing}`);
        } else if (!startFailed && !triedZxingStop && !html5QrCode) { // Solo si no falló al inicio Y no se intentó parar Zxing Y html5QrCode es null
            log('STOPPED_NONE', 'No se detectó instancia activa para detener (ni Html5 ni Zxing).');
        } else if (startFailed) {
             log('STOPPED_ON_FAIL', `Limpieza ejecutada después de fallo de inicio. Html5 detenido: ${stoppedHtml5}, Zxing detenido: ${stoppedZxing}`);
        } else {
             // Si se intentó detener zxing (triedZxingStop) pero stoppedZxing es false Y no es startFailed
             log('STOPPED_NONE_ACTIVE?', `No se marcó como detenido, pero se intentó. Html5: ${stoppedHtml5}, Zxing Detenido?: ${stoppedZxing}, Intento Zxing?: ${triedZxingStop}`);
        }

    } catch (err) { // Catch global por si algo más falla
        log('STOP_ERROR', `Error inesperado durante la detención: ${err}`, 'error');
    } finally {
         // Limpieza final y lógica de reinicio (sin cambios)
        html5QrCode = null; zxingReader = null; zxingControls = null; // Asegurar limpieza final
        log('STOP_FINALLY', 'Finalizando secuencia de detención.');
        activeCameraId = null;
        const modeWhenStopped = currentScannerMode;
        currentScannerMode = null;
        const modeToRestart = restartRequested;
        restartRequested = null;
        isScannerBusy = false; stopInProgress = false;
        log('STATE_RESET', `isScannerBusy: ${isScannerBusy}, stopInProgress: ${stopInProgress}`);
        if (modeToRestart && !startFailed) {
            log('RESTARTING', `Se programó reinicio para modo: ${modeToRestart}. Iniciando en breve...`);
            if (startTimeoutId) clearTimeout(startTimeoutId);
            startTimeoutId = setTimeout(() => startScanner(modeToRestart), 100);
        } else if (modeToRestart && startFailed) {
             log('RESTART_CANCELLED', `Reinicio para ${modeToRestart} cancelado debido a fallo de inicio previo.`);
        } else { log('NO_RESTART', 'No hay reinicio pendiente o fue cancelado.'); }
    }
}

async function handleDecodedText(decodedText) {
    if (!isScannerBusy || stopInProgress) {
        log('HANDLE_DECODE_SKIP', `Procesamiento omitido. Busy: ${isScannerBusy}, StopInProgress: ${stopInProgress}`);
        return;
    }
    log('HANDLE_DECODE', `Procesando texto decodificado para modo ${currentScannerMode}`);
    const modeBeingHandled = currentScannerMode;
    await stopScanner(); // Detener primero
    log('HANDLE_DECODE_POST_STOP', `Continuando procesamiento para modo ${modeBeingHandled}`);
    if (modeBeingHandled === 'vehicle') { parseVehicleQrData(decodedText); }
    else if (modeBeingHandled === 'dni') { parseDniData(decodedText); }
    else if (modeBeingHandled === 'license') { parseLicenseData(decodedText); }
}

// --- Funciones de Parseo ---
// --- Función parseDniData ACTUALIZADA ---
function parseDniData(data) {
    if (!data) { log('DNI_PARSE', 'Datos de DNI vacíos.', 'warn'); return; }
    const parts = data.split('@');
    log('DNI_PARSE', `Intentando parsear DNI. Partes: ${parts.length}`);

    // Obtener referencias a TODOS los campos relevantes
    const lastNameInput = document.getElementById('person-lastname');
    const firstNameInput = document.getElementById('person-firstname');
    const dniInput = document.getElementById('person-dni');
    const rawInput = document.getElementById('person-dni-raw');
    const genderInput = document.getElementById('person-gender');
    const dobInput = document.getElementById('person-dob');
    const ageInput = document.getElementById('person-age'); // Campo de edad

    // Limpiar campos antes de parsear (importante si el formato falla)
    if (lastNameInput) lastNameInput.value = '';
    if (firstNameInput) firstNameInput.value = '';
    if (dniInput) dniInput.value = '';
    if (genderInput) genderInput.value = ''; // Resetear select
    if (dobInput) dobInput.value = '';     // Limpiar fecha
    if (ageInput) ageInput.value = '';      // Limpiar edad calculada
    if (rawInput) rawInput.value = data;    // Siempre mostrar el raw data

    let parsedDob = null; // Variable para guardar la fecha parseada como objeto Date

    // Formato estándar DNI argentino (ajustar índices si es necesario)
    // 0: Nro Trámite | 1: Apellido | 2: Nombres | 3: Género (M/F) | 4: DNI | 5: Ejemplar (A/B/C..) | 6: Fecha Nac (DD/MM/YYYY) | 7: Fecha Emisión (DD/MM/YYYY) | 8: CUIL (primeros 2 + DNI + último)
    if (parts.length >= 8) {
        log('DNI_PARSE_STANDARD', `Parseando formato estándar. Partes: ${parts.length}`);
        if (lastNameInput) lastNameInput.value = parts[1].trim(); else log('WARN','Elemento person-lastname no encontrado');
        if (firstNameInput) firstNameInput.value = parts[2].trim(); else log('WARN','Elemento person-firstname no encontrado');
        if (dniInput) dniInput.value = parts[4].trim(); else log('WARN','Elemento person-dni no encontrado');

        // Asignar Género
        const genderValue = parts[3].trim().toUpperCase();
        if (genderInput) {
             if (genderValue === 'M' || genderValue === 'F' || genderValue === 'X') {
                 genderInput.value = genderValue;
             } else {
                 genderInput.value = ''; // Dejar en blanco si no coincide
                 log('WARN', `Género no reconocido en DNI: ${genderValue}`);
             }
        } else log('WARN','Elemento person-gender no encontrado');

        // --- SECCIÓN CORREGIDA PARA FECHA DE NACIMIENTO ---
        // Asignar y parsear Fecha de Nacimiento (Formato DD/MM/YYYY a YYYY-MM-DD)
        const dobString = parts[6].trim(); // parts[6] es la fecha DD/MM/YYYY
        if (dobInput) { // Solo si existe el input en el HTML
            const dobParts = dobString.match(/^(\d{2})\/(\d{2})\/(\d{4})$/); // Busca el formato DD/MM/YYYY
            if (dobParts) {
                // Extraer día, mes, año
                const day = dobParts[1];
                const month = dobParts[2];
                const year = dobParts[3];
                // Crear el formato YYYY-MM-DD para el input type="date"
                const formattedDob = `${year}-${month}-${day}`;

                // Asignar el valor formateado al input
                dobInput.value = formattedDob;
                log('DNI_PARSE_INFO', `Fecha Nacimiento asignada (formato DD/MM/YYYY detectado): ${formattedDob}`);

                // Intentar crear un objeto Date para el cálculo de edad
                // Usar los componentes parseados es más fiable que usar la string "YYYY-MM-DD" directamente
                // Recordar que el mes en el constructor de Date es 0-indexado (0=Enero, 1=Febrero...)
                const birthDateObj = new Date(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10));

                // Verificar si el objeto Date resultante es válido
                if (!isNaN(birthDateObj.getTime())) {
                     parsedDob = birthDateObj; // Guardar el objeto Date válido
                     log('DNI_PARSE_INFO', 'Objeto Date para cálculo de edad creado exitosamente.');
                } else {
                    // Esto podría pasar si la fecha extraída es lógicamente inválida (ej. 31/02/1980)
                    log('WARN', `Se detectó formato DD/MM/YYYY pero creó una fecha inválida: ${dobString}`);
                    // No limpiamos dobInput.value aquí, dejamos el valor YYYY-MM-DD asignado.
                    // El navegador podría mostrarlo como inválido, o el usuario puede corregirlo.
                }

            } else { // Si el string de fecha no coincide con DD/MM/YYYY
                log('WARN', `Formato de Fecha Nacimiento no reconocido en DNI: ${dobString}. Se deja campo vacío.`);
                 dobInput.value = ''; // Limpiar el campo si el formato no es el esperado
            }
        } else { // Si no existe el input en el HTML
            log('WARN','Elemento person-dob no encontrado en el HTML');
        }
        // --- FIN SECCIÓN CORREGIDA ---

        log('DNI_PARSE_SUCCESS', `DNI: ${parts[4]}, Apellido: ${parts[1]}`);

    } else if (parts.length >= 6) { // Fallback a formato simple (sin género/fecha)
         log('DNI_PARSE_SIMPLE', `Usando formato simple. Partes: ${parts.length}`);
         if(lastNameInput) lastNameInput.value = parts[1].trim();
         if(firstNameInput) firstNameInput.value = parts[2].trim();
         if(dniInput) dniInput.value = parts[4].trim();
    } else { // Si no hay suficientes partes ni para el formato simple
        alert("Formato de DNI no reconocido. Verifique los datos o ingrese manualmente.");
        log('DNI_PARSE_FAIL', 'Formato inválido, partes insuficientes: ' + parts.length, 'warn');
        // Los campos ya se limpiaron al inicio, solo queda el raw data
    }

    // Calcular y mostrar edad SI tenemos un objeto Date de nacimiento válido
    if (parsedDob && ageInput) {
        ageInput.value = calculateAge(parsedDob); // Llama a la función auxiliar
    } else if (ageInput) {
        ageInput.value = ''; // Limpiar campo edad si no hay fecha válida
    }
}

// --- Nueva función para calcular edad ---
function calculateAge(birthDate) {
  if (!birthDate || !(birthDate instanceof Date) || isNaN(birthDate.getTime())) {
    return ''; // Retorna vacío si la fecha no es válida
  }
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  // Ajustar si aún no ha pasado el cumpleaños este año
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age >= 0 ? age : ''; // Asegurar que la edad no sea negativa
}

function parseVehicleQrData(data) {
    if (!data) { log('QR_PARSE', 'Datos de QR Vehicular vacíos.', 'warn'); return; }
    log('QR_PARSE', `Intentando parsear QR Vehicular con datos:\n${data}`);

    const plateInput = document.getElementById('vehicle-plate');
    const credentialInput = document.getElementById('vehicle-credential');
    const chassisInput = document.getElementById('vehicle-chassis');
    const notesInput = document.getElementById('vehicle-notes');

    if (!plateInput) log('WARN', 'Elemento vehicle-plate no encontrado');
    if (!credentialInput) log('WARN', 'Elemento vehicle-credential no encontrado');
    if (!chassisInput) log('WARN', 'Elemento vehicle-chassis no encontrado');
    if (!notesInput) log('WARN', 'Elemento vehicle-notes no encontrado');

    if (plateInput) plateInput.value = '';
    if (credentialInput) credentialInput.value = '';
    if (chassisInput) chassisInput.value = '';
    if (notesInput) notesInput.value = '';

    const lines = data.trim().split(/\r?\n/);
    log('QR_PARSE', `QR dividido en ${lines.length} líneas.`);

    let parsedOk = false;
    if (lines.length >= 3) {
        const credencial = lines[0].trim();
        const patente = lines[1].trim();
        const chasis = lines[2].trim();
        log('QR_PARSE_LINES', `L1 (Credencial): ${credencial}, L2 (Patente): ${patente}, L3 (Chasis): ${chasis}`);
        if (credentialInput) credentialInput.value = credencial;
        if (plateInput) plateInput.value = patente;
        if (chassisInput) chassisInput.value = chasis;
        parsedOk = true;
        log('QR_PARSE_SUCCESS', `Datos asignados: Patente=${patente}, Credencial=${credencial}, Chasis=${chasis}`);
    } else if (lines.length === 1) {
        log('QR_PARSE_SINGLE_LINE', 'Solo una línea detectada, intentando regex de patente...');
        const platePattern = /([A-Z]{2}\s?[0-9]{3}\s?[A-Z]{2})|([A-Z]{3}\s?[0-9]{3})|([0-9]{3}\s?[A-Z]{3})/;
        let plateMatch = lines[0].match(platePattern);
        let extractedPlate = null;
        if (plateMatch) {
            extractedPlate = plateMatch.slice(1).find(match => match !== undefined);
            if (extractedPlate) extractedPlate = extractedPlate.replace(/\s/g, '');
        }
        if (extractedPlate && plateInput) {
            plateInput.value = extractedPlate;
            if (notesInput) notesInput.value = `QR Raw (1 línea):\n${data}`;
            parsedOk = true;
            log('QR_PARSE_SUCCESS', `Patente extraída de línea única: ${extractedPlate}`);
        } else {
             if (notesInput) notesInput.value = `QR Raw (1 línea, sin patente):\n${data}`;
        }
    } else {
         if (notesInput) notesInput.value = `QR Raw (${lines.length} líneas):\n${data}`;
         log('QR_PARSE_WARN', `Número inesperado de líneas (${lines.length}). Datos completos puestos en Notas.`);
    }

    if (!parsedOk && lines.length > 0) {
        alert("Formato de QR Vehicular no reconocido. Datos completos copiados a Notas.");
        log('QR_PARSE_FAIL', 'No se pudo parsear estructura de 3 líneas ni extraer patente de 1 línea.');
    } else if (lines.length === 0){
         log('QR_PARSE_FAIL', 'QR vacío o inválido.');
    }
}

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', () => {
    log('DOM_LOADED', 'DOM cargado. Configurando listeners...');

    const personModal = document.getElementById('identifyPersonModal');
    const vehicleModal = document.getElementById('identifyVehicleModal');

    if (personModal) {
        personModal.addEventListener('show.bs.modal', event => {
            log('MODAL_SHOW', 'Abriendo modal Persona. Limpiando formulario.');
            const form = document.getElementById('identify-person-form');
            if (form) {
                form.reset();
                const rawInput = document.getElementById('person-dni-raw');
                const ageInput = document.getElementById('person-age');
                if (rawInput) rawInput.value = '';
                if (ageInput) ageInput.value = '';
                const errorDiv = document.getElementById('identify-person-error');
                if (errorDiv) { errorDiv.classList.add('d-none'); errorDiv.textContent = ''; }
            }
            const scannerContainer = document.getElementById('scanner-container-person');
            if (scannerContainer) scannerContainer.style.display = 'none';
    
            // Mostrar popup de identificación activa
            if (window.agentMarker && typeof window.showIdentifyingPopup === 'function') {
                window.showIdentifyingPopup(window.agentMarker, {
                    last_name: '', first_names: '', dni_number: '', dob: ''
                });
            }
        });
    
        personModal.addEventListener('hide.bs.modal', () => {
            // Restaurar popup normal
            if (window.agentMarker && typeof window.restoreAgentPopup === 'function') {
                window.restoreAgentPopup(
                    window.agentMarker,
                    currentLatitude || -40.81,
                    currentLongitude || -62.99,
                    currentAccuracy || null
                );
            }
        });
    }
    
    if (vehicleModal) {
        vehicleModal.addEventListener('show.bs.modal', event => {
            log('MODAL_SHOW', 'Abriendo modal Vehículo. Limpiando formulario.');
            const form = document.getElementById('identify-vehicle-form');
            if (form) {
                form.reset();
                const errorDiv = document.getElementById('identify-vehicle-error');
                if (errorDiv) { errorDiv.classList.add('d-none'); errorDiv.textContent = ''; }
            }
            const scannerContainer = document.getElementById('scanner-container-vehicle');
            if (scannerContainer) scannerContainer.style.display = 'none';
    
            // Mostrar popup de identificación activa para vehículo
            if (window.agentMarker && typeof window.showIdentifyingPopup === 'function') {
                window.showIdentifyingPopup(window.agentMarker, {
                    last_name: 'Vehículo', first_names: 'en revisión', dni_number: '', dob: ''
                });
            }
        });
    
        vehicleModal.addEventListener('hide.bs.modal', () => {
            if (window.agentMarker && typeof window.restoreAgentPopup === 'function') {
                window.restoreAgentPopup(
                    window.agentMarker,
                    currentLatitude || -40.81,
                    currentLongitude || -62.99,
                    currentAccuracy || null
                );
            }
        });
    }
    
    const btnScanLicense     = document.getElementById('btn-scan-license');
const btnStopScanLicense = document.getElementById('btn-stop-scan-license');

if (btnScanLicense) {
  btnScanLicense.addEventListener('click', () => {
    log('LISTENER_CLICK', 'Clic en btn-scan-license');
    startScanner('license');
  });
  log('LISTENER_ADDED', 'Listener para btn-scan-license añadido.');
}
if (btnStopScanLicense) {
  btnStopScanLicense.addEventListener('click', stopScanner);
  log('LISTENER_ADDED', 'Listener para btn-stop-scan-license añadido.');
}
    // --- NUEVO: Listener para calcular edad automáticamente ---
    const dobInput = document.getElementById('person-dob');
    const ageInput = document.getElementById('person-age');
    if (dobInput && ageInput) {
        dobInput.addEventListener('change', () => {
             log('DOB_CHANGE', 'Cambió la fecha de nacimiento.');
             const dobValue = dobInput.value; // Formato YYYY-MM-DD
             if (dobValue) {
                 // Convertir YYYY-MM-DD a objeto Date (cuidado con zonas horarias si es crítico)
                 // Simplemente usar el constructor de Date suele ser suficiente para esto
                 const birthDate = new Date(dobValue + 'T00:00:00'); // Añadir hora para evitar problemas de zona horaria
                 if (!isNaN(birthDate.getTime())) {
                     ageInput.value = calculateAge(birthDate);
                 } else {
                      ageInput.value = ''; // Limpiar si la fecha es inválida
                 }
             } else {
                 ageInput.value = ''; // Limpiar si se borra la fecha
             }
        });
        log('LISTENER_ADDED', 'Listener para cálculo de edad añadido.');
    } else {
        log('WARN', 'No se encontraron los campos de Fecha Nacimiento o Edad para el listener.');
    }
    // --- FIN Listener de Edad ---

    ['identifyPersonModal', 'identifyVehicleModal'].forEach(id => {
        const modalElement = document.getElementById(id);
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', (event) => {
                log('MODAL_CLOSE', `Modal cerrado: ${event.target.id}. Deteniendo escáner si está activo.`);
                stopScanner();
            });
            modalElement.addEventListener('hide.bs.modal', (event) => { /* ... focus logic (opcional) ... */ });
        } else {
            log('WARN', `Elemento modal con ID ${id} no encontrado durante setup.`);
        }
    });

     const btnScanDni = document.getElementById('btn-scan-dni');
     const btnScanVehicle = document.getElementById('btn-scan-vehicle');

     if (btnScanDni) {
         btnScanDni.addEventListener('click', () => {
            console.log(`%c[LISTENER_CLICK] Clic detectado en btn-scan-dni a las ${Date.now()}`, 'color: blue; font-weight: bold;');
            console.trace('[LISTENER_CLICK] Trace btn-scan-dni');
            startScanner('dni');
         });
         log('LISTENER_ADDED', 'Listener para btn-scan-dni añadido.');
     } else { log('WARN', 'Botón con ID btn-scan-dni no encontrado.'); }

     if (btnScanVehicle) {
         btnScanVehicle.addEventListener('click', () => {
            console.log(`%c[LISTENER_CLICK] Clic detectado en btn-scan-vehicle a las ${Date.now()}`, 'color: blue; font-weight: bold;');
            console.trace('[LISTENER_CLICK] Trace btn-scan-vehicle');
            startScanner('vehicle');
         });
         log('LISTENER_ADDED', 'Listener para btn-scan-vehicle añadido.');
     } else { log('WARN', 'Botón con ID btn-scan-vehicle no encontrado.'); }

     const btnStopScanPerson = document.getElementById('btn-stop-scan-person');
     const btnStopScanVehicle = document.getElementById('btn-stop-scan-vehicle');

     if (btnStopScanPerson) { btnStopScanPerson.addEventListener('click', stopScanner); log('LISTENER_ADDED', 'Listener para btn-stop-scan-person añadido.'); }
     else { log('WARN', 'Botón con ID btn-stop-scan-person no encontrado.'); }

     if (btnStopScanVehicle) { btnStopScanVehicle.addEventListener('click', stopScanner); log('LISTENER_ADDED', 'Listener para btn-stop-scan-vehicle añadido.'); }
     else { log('WARN', 'Botón con ID btn-stop-scan-vehicle no encontrado.'); }

     log('SCANNER_SETUP', 'Configuración de listeners del escáner completada.');
});

// Botón y contenedor nuevos en tu modal/license.html:
//   <button id="btn-scan-license">Escanear Licencia</button> 
//   <div id="scanner-container-license">…<div id="reader-license"></div>…</div>

//document.getElementById('btn-scan-license')?.addEventListener('click', ()=> startScanner('license'));

async function parseLicenseData(raw) {
  // similar a parseDniData: parar Scanner, mapear campos en inputs específicos
  const data = raw; 
  document.getElementById('license-raw').value = data;
  const parsed = parseDrivingLicense(data); // función inyectada por un <script> que importe el parser
  if (!parsed) { alert("Formato no reconocido"); return; }
  document.getElementById('license-number').value     = parsed.license_number;
  document.getElementById('license-lastname').value   = parsed.last_name;
  document.getElementById('license-firstnames').value = parsed.first_names;
  document.getElementById('license-dob').value        = parsed.dob;
  document.getElementById('license-expiry').value     = parsed.expiration_date;
  document.getElementById('license-categories').value = parsed.categories;
}