<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permisos de Internet: Fundamental para todas las comunicaciones de red -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Permisos de Ubicación: Necesarios para el seguimiento GPS -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Permiso específico para servicio de ubicación en primer plano -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <!-- Permis<PERSON> <PERSON>mara: Para escanear códigos QR y tomar fotos -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Permisos para mantener la aplicación activa en segundo plano -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Permiso para Notificaciones (Necesario para ForegroundService en Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- Permisos de Bluetooth (para evitar advertencias) -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- Características de hardware (opcional pero recomendado para filtrar en Play Store) -->
    <uses-feature android:name="android.hardware.location.gps" android:required="true" />
    <uses-feature android:name="android.hardware.camera" android:required="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Opera"
        tools:targetApi="34"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true">
        <!-- usesCleartextTraffic permite conexiones HTTP, pero usamos HTTPS para producción -->

        <!-- Actividad Principal (punto de entrada) -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Servicio para mantener la aplicación activa en segundo plano -->
        <service
            android:name=".BackgroundService"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                      android:value="keepAlive" />
        </service>

        <!-- Servicio de seguimiento GPS mejorado -->
        <service
            android:name=".LocationTrackingService"
            android:exported="false"
            android:foregroundServiceType="location" />

    </application>

</manifest>